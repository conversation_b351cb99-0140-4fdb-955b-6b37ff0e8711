'use client';

import React, { useState } from 'react';

export default function ContactForm() {
  const [formData, setFormData] = useState({ name: 'import { UnifiedButton } from '@/components/ui/UnifiedButton';
', email: '', message: '', honeypot: '' });
  const [status, setStatus] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Honeypot check - jeśli wypełnione, to spam
    if (formData.honeypot) {
      return;
    }

    setIsSubmitting(true);
    setStatus('Wysyłanie...');

    try {
      const response = await fetch('https://api.web3forms.com/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          access_key: 'YOUR_WEB3FORMS_ACCESS_KEY', // Zastąp swoim kluczem z web3forms.com
          name: formData.name,
          email: formData.email,
          message: formData.message,
          subject: `Nowa wiadomość z BAKASANA od ${formData.name}`,
          from_name: 'BAKASANA',
          to_email: '<EMAIL>'
        })
      });

      const result = await response.json();

      if (result.success) {
        setStatus('Wiadomość wysłana. Dziękujemy, odpowiemy wkrótce.');
        setFormData({ name: '', email: '', message: '', honeypot: '' });
      } else {
        throw new Error('Błąd wysyłania');
      }
    } catch (error) {
      console.error('Error:', error);
      setStatus('Wystąpił błąd. Spróbuj ponownie lub napisz bezpoś<NAME_EMAIL>');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setStatus(''), 8000);
    }
  };

  const socialLinks = [
    {
      href: "https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr",
      label: "Instagram",
      aria: "Profil na Instagramie"
    },
    {
      href: "https://www.facebook.com/p/Fly-with-bakasana-100077568306563/",
      label: "Facebook",
      aria: "Profil na Facebooku"
    },
    {
      href: "mailto:<EMAIL>",
      label: "Email",
      aria: "Kontakt email"
    }
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-start max-w-5xl mx-auto">
      {/* FORMULARZ KONTAKTOWY - Ultra-minimal */}
      <div className="space-y-lg">
        <div className="text-center lg:text-left">
          <h2 className="section-header mb-md">Napisz do nas</h2>
          <p className="body-text opacity-80">
            Każda wiadomość jest dla nas ważna
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-lg">
          <div>
            <label htmlFor="name" className="block subtle-text mb-3">Imię</label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="w-full px-0 py-4 bg-transparent border-0 border-b border-stone/30 focus:border-charcoal-gold focus:outline-none transition-colors text-charcoal placeholder-stone/50"
              placeholder="Twoje imię"
            />
          </div>

          <div>
            <label htmlFor="email" className="block subtle-text mb-3">Email</label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="w-full px-0 py-4 bg-transparent border-0 border-b border-stone/30 focus:border-charcoal-gold focus:outline-none transition-colors text-charcoal placeholder-stone/50"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label htmlFor="message" className="block subtle-text mb-3">Wiadomość</label>
            <textarea
              id="message"
              value={formData.message}
              onChange={handleChange}
              required
              rows={6}
              className="w-full px-0 py-4 bg-transparent border-0 border-b border-stone/30 focus:border-charcoal-gold focus:outline-none transition-colors text-charcoal placeholder-stone/50 resize-none"
              placeholder="Podziel się swoimi myślami..."
            />
          </div>

          {/* Honeypot field - ukryte dla ludzi, widoczne dla botów */}
          <input
            type="text"
            id="honeypot"
            name="honeypot"
            value={formData.honeypot}
            onChange={handleChange}
            style={{ display: 'none' }}
            tabIndex="-1"
            autoComplete="off"
          />

          <div className="pt-8">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`btn-ghost btn-primary ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isSubmitting ? 'Wysyłanie...' : 'Wyślij Wiadomość'}
            </button>

            {status && (
              <p className="text-sm text-charcoal/70 font-light mt-sm max-w-xs">
                {status}
              </p>
            )}
          </div>
        </form>
      </div>

      {/* KONTAKT - Ultra-minimal */}
      <div className="space-y-lg">
        <div className="text-center lg:text-left">
          <h3 className="section-header mb-md">Znajdź nas</h3>
          <p className="body-text opacity-80 mb-lg">
            Połączmy się w przestrzeni cyfrowej
          </p>
        </div>

        <div className="space-y-md">
          {socialLinks.map((link) => (
            <a
              key={link.label}
              href={link.href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={link.aria}
              className="block p-6 hover:opacity-70 transition-opacity duration-200 text-center lg:text-left"
            >
              <h4 className="font-light text-charcoal mb-2 tracking-wide text-lg">
                {link.label}
              </h4>
              <p className="text-sm text-stone font-light">
                {link.label === 'Instagram' && 'Codzienne inspiracje'}
                {link.label === 'Facebook' && 'Społeczność BAKASANA'}
                {link.label === 'Email' && 'Bezpośredni kontakt'}
              </p>
            </a>
          ))}
        </div>

        {/* SACRED DIVIDER */}
        <div className="flex items-center justify-center lg:justify-start my-12">
          <div className="flex items-center gap-sm text-charcoal-gold/60">
            <div className="w-12 h-px bg-charcoal-gold/30"></div>
            <span className="text-lg opacity-60">ॐ</span>
            <div className="w-12 h-px bg-charcoal-gold/30"></div>
          </div>
        </div>

        <div className="text-center lg:text-left">
          <p className="text-sm text-stone font-light italic tracking-wide">
            "Każda podróż zaczyna się od jednego kroku..."
          </p>
          <p className="text-xs text-charcoal-gold font-light tracking-wide uppercase mt-2">
            Om Swastiastu
          </p>
        </div>
      </div>
    </div>
  );
}