'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useResponsiveStyles, getSectionStyles } from '../../components/OnlineClassesStyles';
import TransformationCTA from '../../components/TransformationCTA';
import { HeroTitle, CardTitle, BodyText } from '@/components/ui/UnifiedTypography';



export default function OMniePage() {
  const { isDesktop, isTablet, isMobile } = useResponsiveStyles();
  const sectionStyles = getSectionStyles(isDesktop, isTablet, isMobile);

  return (
    <main className="bg-sanctuary min-h-screen">
      {/* HERO SECTION - Magazine Style Header */}
      <section className="magazine-hero">
        <div className="magazine-hero-content">
          <div className="magazine-header-line"></div>
          
          <HeroTitle className="magazine-title">
            <PERSON>
          </HeroTitle>

          <p className="magazine-subtitle">
            Instruktorka jogi RYT 500 • Fizjoterapeutka
          </p>

          <div className="magazine-meta">
            Przewodniczka transformacyjnych podróży
          </div>
          
          <div className="magazine-header-line"></div>
        </div>
      </section>

      {/* SEKCJA GŁÓWNA - Dwukolumnowa */}
      <section className="container mx-auto px-container-sm md:px-container-md lg:px-container-lg py-section">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-lg md:gap-xl lg:gap-2xl items-start">
          {/* Kolumna tekstowa (lewa) */}
          <div>
            <h2 style={sectionStyles.sectionTitle}>
              Moja droga
            </h2>

            <BodyText className="mb-lg">
              Prawdziwa transformacja dzieje się poza matą — w codzienności, w sposób, w jaki 
              oddychamy, poruszamy się i odnosimy do świata.
            </p>

            <BodyText className="mb-2xl text-left">
              Bali i Sri Lanka nauczyły mnie słuchania tej prawdziwej ciszy, która mieszka 
              w sercu. Każda podróż to powrót do naszej autentycznej natury, do tego kim 
              naprawdę jesteśmy, gdy zdjemy wszystkie maski i społeczne oczekiwania.
            </div>

            <div className="mb-2xl">
              <CardTitle className="mb-lg">
                Moje podejście
              </CardTitle>
              
              <div>
                {[
                  'Łączę wiedzę medyczną z duchową praktyką',
                  'Tworzę bezpieczną przestrzeń eksploracji',
                  'Wspieram budowanie świadomej relacji z ciałem'
                ].map((approach, index) => (
                  <div key={index} style={{
                    padding: '16px 0',
                    paddingLeft: '24px',
                    borderLeft: '1px solid rgba(196, 165, 117, 0.1)',
                    position: 'relative'
                  }}>
                    <span style={{
                      fontSize: '14px',
                      fontFamily: 'Inter',
                      fontWeight: 300,
                      color: '#4A4744'
                    }}>
                      {approach}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Kolumna wizualna (prawa) */}
          <div className="h-[300px] md:h-[580px] relative aspect-[3/4]">
            <Image
              src="/images/profile/omnie-opt.webp"
              alt="Julia Jakubowicz - instruktorka jogi"
              fill
              style={{
                objectFit: 'cover',
                filter: 'grayscale(100%) brightness(1.05)'
              }}
              sizes={isMobile ? '100vw' : '45vw'}
              quality={95}
            />
          </div>
        </div>
      </section>

      {/* LINIA PODZIAŁU */}
      <div style={sectionStyles.divider}></div>

      {/* SEKCJA DOŚWIADCZENIE - Odwrócona dwukolumnowa */}
      <section className="container mx-auto px-container-sm md:px-container-md lg:px-container-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-lg md:gap-xl lg:gap-2xl items-start">
          {/* Kolumna wizualna (lewa) */}
          <div className="h-[300px] md:h-[480px] bg-sanctuary/30 flex items-center justify-center relative">
            <div className="flex items-center justify-center gap-2 md:gap-5">
              <div className="w-10 h-10 md:w-15 md:h-15 border border-terra/15 transform translate-x-1 md:translate-x-2"></div>
              <div className="w-10 h-10 md:w-15 md:h-15 border border-terra/15 transform -translate-x-1 md:-translate-x-2"></div>
              <div className="w-10 h-10 md:w-15 md:h-15 border border-terra/15 transform -translate-x-4 md:-translate-x-8"></div>
            </div>
          </div>

          {/* Kolumna tekstowa (prawa) */}
          <div>
            <h2 style={sectionStyles.sectionTitle}>
              Doświadczenie
            </h2>

            <p style={{
              fontSize: '17px',
              fontFamily: 'Inter',
              fontWeight: 300,
              color: '#5B5754',
              lineHeight: '1.8',
              marginBottom: '32px'
            }}>
              Ponad 8 lat praktyki i nauczania jogi w różnych formach i miejscach.
            </p>

            <div style={{
              fontSize: '15px',
              fontFamily: 'Inter',
              fontWeight: 300,
              color: '#3D3A37',
              lineHeight: '1.9',
              letterSpacing: '0.02em',
              marginBottom: '56px',
              textAlign: 'left'
            }}>
              Od sceptycznej fizjoterapeutki do przewodniczki duchowych transformacji. 
              Każda podróż na Bali i Sri Lanka to lekcja pokory wobec starożytnej mądrości.
            </div>

            <div style={{marginBottom: '64px'}}>
              <h3 style={{
                fontSize: '20px',
                fontFamily: 'Cormorant Garamond',
                fontWeight: 400,
                color: '#3D3A37',
                marginBottom: '28px'
              }}>
                Kwalifikacje i certyfikaty
              </h3>
              
              <div>
                {[
                  { name: 'RYT 500 - Certified Yoga Teacher', duration: 'Yoga Alliance', note: 'Registered Yoga Alliance' },
                  { name: '8 lat praktyki nauczania', duration: 'Doświadczenie', note: 'Prowadzenie grup i sesji indywidualnych' },
                  { name: 'Fizjoterapeutka', duration: 'Terapia manualna', note: 'Medyczne podejście do jogi' },
                  { name: '200+ uczniów', duration: 'Azja', note: 'Retreaty na Bali i Sri Lanka' }
                ].map((option, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '20px 0',
                    borderBottom: index === 3 ? 'none' : '1px solid rgba(139, 133, 127, 0.15)'
                  }}>
                    <div>
                      <h4 style={{
                        fontSize: '15px',
                        fontFamily: 'Inter',
                        fontWeight: 400,
                        color: '#3D3A37',
                        margin: '0 0 4px 0'
                      }}>
                        {option.name}
                      </h4>
                      <p style={{
                        fontSize: '13px',
                        fontFamily: 'Inter',
                        fontWeight: 300,
                        color: '#8B857F',
                        margin: 0
                      }}>
                        {option.duration}
                      </p>
                    </div>
                    <span style={{
                      fontSize: '13px',
                      fontFamily: 'Inter',
                      fontWeight: 300,
                      color: '#8B857F'
                    }}>
                      {option.note}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* OSTATNIA SEKCJA - KONTAKT */}
      <TransformationCTA />
    </main>
  );
}