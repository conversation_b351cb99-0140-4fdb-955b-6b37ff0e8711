'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import { usePathname } from 'next/navigation';
import PerformantWhatsApp from './PerformantWhatsApp';
import { NavLink } from '@/components/ui/UnifiedTypography';

export default function GhostNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY;
    
    // Ukryj navbar przy scroll down, pokaż przy scroll up
    if (currentScrollY > lastScrollY && currentScrollY > 100) {
      setIsVisible(false);
    } else {
      setIsVisible(true);
    }
    
    setScrolled(currentScrollY > 20);
    setLastScrollY(currentScrollY);
  }, [lastScrollY]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  const isActiveLink = (href) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  };

  const navItems = useMemo(() => [
    { 
      href: '/retreaty', 
      label: 'Retreaty',
      dropdown: [
        { href: '/program?destination=bali', label: 'Bali' },
        { href: '/program?destination=srilanka', label: 'Sri Lanka' }
      ]
    },
    { href: '/zajecia-online', label: 'Zajęcia Online' },
    { href: '/o-mnie', label: 'O mnie' },
    { href: '/blog', label: 'Blog' },
    { href: '/kontakt', label: 'Kontakt' }
  ], []);

  return (
    <>
      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-700 ease-out ${
        isVisible ? 'transform translate-y-0' : 'transform -translate-y-full'
      } ${
        scrolled 
          ? 'bg-white/40 backdrop-blur-[30px] shadow-[0_1px_20px_rgba(139,115,85,0.03)]' 
          : 'bg-transparent'
      }`}>
        <div className="mx-auto px-hero-padding lg:px-16">
          <div className="flex items-center justify-between h-16">
            
            {/* Logo - Ghost style - prawie niewidoczny */}
            <Link 
              href="/" 
              className="group relative font-cormorant font-light text-enterprise-brown/60 transition-all duration-700 hover:text-enterprise-brown"
              style={{ 
                fontSize: '18px',
                letterSpacing: '1px',
                fontWeight: '300'
              }}
            >
              BAKASANA
              <span className="absolute -bottom-1 left-0 w-0 h-[0.5px] bg-enterprise-brown/50 transition-all duration-500 group-hover:w-full"></span>
            </Link>

            {/* Desktop Menu - Pojawia się przy scrollowaniu lub hover */}
            <div className={`hidden lg:flex items-center space-x-8 transition-all duration-700 ${
              scrolled ? 'opacity-100' : 'opacity-0 hover:opacity-100'
            }`}>
              {navItems.map((item) => (
                <div key={item.href} className="relative group">
                  <Link
                    href={item.href}
                    className="relative group"
                  >
                    <NavLink active={isActiveLink(item.href)}>
                      {item.label}
                    </NavLink>
                    
                    {/* Aktywny wskaźnik - elegancka linia */}
                    {isActiveLink(item.href) && (
                      <span className="absolute -bottom-1 left-0 right-0 h-[1px] bg-enterprise-brown opacity-60"></span>
                    )}
                    
                    {/* Hover efekt - subtelna linia */}
                    <span className="absolute -bottom-1 left-0 w-0 h-[0.5px] bg-enterprise-brown/40 transition-all duration-500 group-hover:w-full"></span>
                  </Link>

                  {/* Dropdown Menu */}
                  {item.dropdown && (
                    <div className="absolute top-full left-0 mt-2 bg-white/95 backdrop-blur-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                      <div className="py-2 min-w-[140px]">
                        {item.dropdown.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.href}
                            href={dropdownItem.href}
                            className="block px-container-sm py-2 hover:bg-whisper/50 transition-all duration-200"
                          >
                            <NavLink className="text-xs">
                              {dropdownItem.label}
                            </NavLink>
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* WhatsApp Button - Desktop */}
            <div className="hidden lg:block">
              <PerformantWhatsApp 
                size="md"
                variant="button"
              />
            </div>

            {/* Mobile Menu Button - Maksymalnie subtelny */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-2 text-enterprise-brown/60 hover:text-enterprise-brown transition-all duration-300"
              aria-label="Toggle menu"
            >
              <div className="w-4 h-4 flex flex-col justify-center items-center">
                <span className={`w-3 h-[0.5px] bg-current transition-all duration-300 transform ${
                  isMenuOpen ? 'rotate-45 translate-y-[1px]' : 'mb-1'
                }`}></span>
                <span className={`w-3 h-[0.5px] bg-current transition-all duration-300 ${
                  isMenuOpen ? '-rotate-45 -translate-y-[1px]' : ''
                }`}></span>
              </div>
            </button>
          </div>
        </div>

        {/* Mobile Menu - Ghost style */}
        <div className={`lg:hidden absolute top-full left-0 right-0 bg-white/90 backdrop-blur-[30px] transition-all duration-500 ${
          isMenuOpen 
            ? 'opacity-100 translate-y-0' 
            : 'opacity-0 -translate-y-4 pointer-events-none'
        }`}>
          <div className="px-hero-padding py-6 space-y-sm">
            {navItems.map((item, index) => (
              <Link
                key={item.href}
                href={item.href}
                onClick={() => setIsMenuOpen(false)}
                className="block transition-all duration-500"
                style={{ 
                  animationDelay: `${index * 50}ms`
                }}
              >
                <NavLink active={isActiveLink(item.href)} className="text-sm">
                  {item.label}
                  {isActiveLink(item.href) && (
                    <span className="inline-block ml-2 w-1 h-1 bg-enterprise-brown rectangular opacity-60"></span>
                  )}
                </NavLink>
              </Link>
            ))}
            
            {/* WhatsApp Button - Mobile */}
            <div className="pt-4 border-t border-enterprise-brown/10">
              <PerformantWhatsApp 
                size="md"
                variant="button"
                className="w-full justify-center text-sm font-light tracking-[1px] py-3 px-hero-padding"
              />
            </div>
          </div>
        </div>
      </nav>

      {/* Overlay dla mobile menu - prawie niewidoczny */}
      {isMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/5 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setIsMenuOpen(false)}
        />
      )}

      <style jsx>{`
        /* Hover efekt na całym navbar */
        nav:hover .opacity-0 {
          opacity: 1;
        }
        
        @keyframes ghostPulse {
          0%, 100% { opacity: 0.4; }
          50% { opacity: 0.8; }
        }
        
        .animate-ghost-pulse {
          animation: ghostPulse 3s ease-in-out infinite;
        }
      `}</style>
    </>
  );
}