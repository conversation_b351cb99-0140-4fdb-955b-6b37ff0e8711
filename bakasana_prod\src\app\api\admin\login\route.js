import { NextResponse } from 'next/server';

import crypto from 'crypto';
import jwt from 'jsonwebtoken';

const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'BakasanaAdmin2024!';
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// Rate limiting - proste w pamięci (w produkcji użyj Redis)
const loginAttempts = new Map();
const MAX_ATTEMPTS = 5;
const LOCKOUT_TIME = 15 * 60 * 1000; // 15 minut

function getClientIP(request) {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

function isRateLimited(ip) {
  const attempts = loginAttempts.get(ip);
  
  if (!attempts) {
    return false;
  }
  
  // Sprawdź czy lockout wygasł
  if (Date.now() - attempts.lastAttempt > LOCKOUT_TIME) {
    loginAttempts.delete(ip);
    return false;
  }
  
  return attempts.count >= MAX_ATTEMPTS;
}

function recordFailedAttempt(ip) {
  const attempts = loginAttempts.get(ip) || { count: 0, lastAttempt: 0 };
  
  attempts.count += 1;
  attempts.lastAttempt = Date.now();
  
  loginAttempts.set(ip, attempts);
}

function clearFailedAttempts(ip) {
  loginAttempts.delete(ip);
}

export async function POST(request) {
  try {
    const clientIP = getClientIP(request);
    
    // Sprawdź rate limiting
    if (isRateLimited(clientIP)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Zbyt wiele nieudanych prób logowania. Spróbuj ponownie za 15 minut.',
          lockoutTime: LOCKOUT_TIME / 1000 / 60 // w minutach
        },
        { status: 429 }
      );
    }

    const { password } = await request.json();

    // Walidacja hasła
    if (!password) {
      recordFailedAttempt(clientIP);
      return NextResponse.json(
        { success: false, error: 'Hasło jest wymagane' },
        { status: 400 }
      );
    }

    // Sprawdź hasło (w produkcji użyj bcrypt)
    const isValidPassword = await verifyPassword(password, ADMIN_PASSWORD);
    
    if (!isValidPassword) {
      recordFailedAttempt(clientIP);
      
      // Log nieudanej próby logowania
      console.warn(`Failed admin login attempt from IP: ${clientIP} at ${new Date().toISOString()}`);
      
      return NextResponse.json(
        { success: false, error: 'Nieprawidłowe hasło' },
        { status: 401 }
      );
    }

    // Wyczyść nieudane próby po udanym logowaniu
    clearFailedAttempts(clientIP);

    // Generuj JWT token
    const token = jwt.sign(
      { 
        role: 'admin',
        ip: clientIP,
        loginTime: Date.now()
      },
      JWT_SECRET,
      { 
        expiresIn: '24h',
        issuer: 'bakasana-travel-admin',
        audience: 'bakasana-travel-app'
      }
    );

    // Log udanego logowania
    console.log(`Successful admin login from IP: ${clientIP} at ${new Date().toISOString()}`);

    return NextResponse.json({
      success: true,
      token,
      expiresIn: 24 * 60 * 60, // 24 godziny w sekundach
      message: 'Zalogowano pomyślnie'
    });

  } catch (error) {
    console.error('Admin login error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Błąd serwera podczas logowania',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Funkcja do weryfikacji hasła (w produkcji użyj bcrypt)
async function verifyPassword(inputPassword, storedPassword) {
  // Proste porównanie - w produkcji użyj bcrypt.compare()
  return inputPassword === storedPassword;
}

// GET endpoint dla sprawdzenia statusu
export async function GET() {
  return NextResponse.json({
    message: 'Admin login endpoint is working',
    timestamp: new Date().toISOString(),
    rateLimit: {
      maxAttempts: MAX_ATTEMPTS,
      lockoutTimeMinutes: LOCKOUT_TIME / 1000 / 60
    }
  });
}
