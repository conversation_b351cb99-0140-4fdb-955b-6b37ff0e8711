'use client';

import { useState, useEffect } from 'react';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import {  motion, useScroll, useTransform  } from '@/components/ui/UnifiedButton';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import Image from 'next/image';

interface HeroStats {
  number: string;
  label: string;
}

const heroStats: HeroStats[] = [
  { number: '8+', label: 'Lat Doświadczenia' },
  { number: '200+', label: 'Zadowolonych Klientów' },
  { number: '15+', label: 'Przeprowadzonych Retreatów' },
  { number: '2', label: 'Egzotyczne Kraje' }
];

const OldMoneyHero: React.FC = () => {
  const [mounted, setMounted] = useState(false);
  const { scrollY } = useScroll();
  
  // Subtle parallax effect for mountains
  const mountainY = useTransform(scrollY, [0, 1000], [0, 200]);
  
  // Fade out hero content on scroll
  const heroOpacity = useTransform(scrollY, [0, 400], [1, 0]);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <section className="relative min-h-screen overflow-hidden bg-gradient-to-b from-parchment via-sanctuary to-white">
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-hero-overlay via-white/70 to-white/90 z-[1]" />
      
      <div className="relative z-10 container mx-auto px-hero-padding pt-nav-height">
        <motion.div 
          className="flex flex-col items-center justify-center min-h-screen text-center"
          style={{ opacity: heroOpacity }}
        >
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-md"
          >
            <span className="inline-block px-container-sm py-2 text-micro text-enterprise-brown bg-white/80 backdrop-blur-sm border border-enterprise-brown/20 tracking-[3.5px] uppercase font-medium">
              🌴 RETREATY JOGI • BALI &amp; SRI LANKA
            </span>
          </motion.div>

          {/* Main Title */}
          <motion.h1
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.2, delay: 0.4 }}
            className="font-cormorant text-hero-massive text-charcoal mb-sm -mt-2"
          >
            BAKASANA
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="font-cormorant text-subtitle text-enterprise-brown/80 italic mb-lg"
          >
            ~ jóga jest drogą ciszy ~
          </motion.p>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-body-lg text-ash max-w-[620px] mx-auto mb-2xl leading-relaxed"
          >
            Odkryj transformującą moc jogi w duchowych sercach Azji. Nasze retreaty to połączenie 
            tradycyjnej praktyki z luksusowym komfortem, tworząc przestrzeń dla głębokiej przemiany 
            w otoczeniu niesamowitych krajobrazów Bali i Sri Lanki.
          </motion.p>

          {/* Stats Grid */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-20 max-w-4xl mx-auto mb-2xl"
          >
            {heroStats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.2 + index * 0.1 }}
                className="text-center"
              >
                <div className="text-[42px] font-extralight text-enterprise-brown mb-2">
                  {stat.number}
                </div>
                <div className="text-micro text-sage uppercase tracking-[2px] font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
            className="flex flex-col sm:flex-row gap-lg justify-center items-center"
          >
            <UnifiedButton variant="primary" size="lg">
              <span className="relative z-10">Przegląd Harmonogramu</span>
              <div className="absolute inset-0 bg-enterprise-brown transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
            </UnifiedButton>
            
            <UnifiedButton variant="primary" size="lg">
              <span className="relative z-10">Rezerwuj Konsultację</span>
              <div className="absolute inset-0 bg-terra transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
            </UnifiedButton>
          </motion.div>
        </motion.div>
      </div>

      {/* Side Form - Desktop Only */}
      <motion.aside
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 1.0, delay: 1.6 }}
        className="hidden xl:block fixed right-8 top-1/2 -translate-y-1/2 z-20 w-80"
      >
        <div className="bg-pearl backdrop-blur-sm border border-enterprise-brown/10 p-8 shadow-premium-shadow">
          <h3 className="font-cormorant text-heading text-charcoal mb-md text-center">
            Zarezerwuj Konsultację
          </h3>
          
          <form className="space-y-sm">
            <div>
              <input
                type="text"
                placeholder="Imię i nazwisko"
                className="w-full px-container-sm py-3 border border-sage/30 bg-white/80 text-charcoal placeholder-sage focus:outline-none focus:border-enterprise-brown transition-colors duration-300"
              />
            </div>
            
            <div>
              <input
                type="email"
                placeholder="Email"
                className="w-full px-container-sm py-3 border border-sage/30 bg-white/80 text-charcoal placeholder-sage focus:outline-none focus:border-enterprise-brown transition-colors duration-300"
              />
            </div>
            
            <div>
              <input
                type="tel"
                placeholder="Telefon"
                className="w-full px-container-sm py-3 border border-sage/30 bg-white/80 text-charcoal placeholder-sage focus:outline-none focus:border-enterprise-brown transition-colors duration-300"
              />
            </div>
            
            <div>
              <select className="w-full px-container-sm py-3 border border-sage/30 bg-white/80 text-charcoal focus:outline-none focus:border-enterprise-brown transition-colors duration-300">
                <option>Wybierz destinację</option>
                <option>Bali - Ubud</option>
                <option>Sri Lanka - Południe</option>
                <option>Zajęcia Online</option>
              </select>
            </div>
            
            <button
              type="submit"
              className="w-full bg-enterprise-brown text-white py-3 px-hero-padding text-small uppercase tracking-[1px] font-medium hover:bg-terra transition-colors duration-300"
            >
              Wyślij Zapytanie
            </button>
          </form>
        </div>
      </motion.aside>

      {/* Mountain Silhouette */}
      <motion.div
        style={{ y: mountainY }}
        className="absolute bottom-0 left-0 right-0 z-[2] h-32 bg-gradient-to-t from-enterprise-brown/20 to-transparent"
      >
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-r from-enterprise-brown/10 via-sand/15 to-enterprise-brown/10" />
        
        {/* Mountain SVG silhouette */}
        <svg
          className="absolute bottom-0 left-0 right-0 w-full h-full"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
        >
          <path
            d="M0,120 L0,40 L200,20 L400,60 L600,10 L800,45 L1000,25 L1200,50 L1200,120 Z"
            fill="url(#mountainGradient)"
            opacity="0.3"
          />
          <defs>
            <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#8B7355" stopOpacity="0.4" />
              <stop offset="100%" stopColor="#8B7355" stopOpacity="0.1" />
            </linearGradient>
          </defs>
        </svg>
      </motion.div>
    </section>
  );
};

export default OldMoneyHero;