import Image from 'next/image';
import Link from 'next/link';

import {  Icon  } from '@/components/ui/IconSystem';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import { HeroTitle, SectionTitle, CardTitle, BodyText } from '@/components/ui/UnifiedTypography';

export const metadata = {
  title: 'Transformacyjne Podróże Azja 2025 - Duchowe Retreaty Jogi | BAKASANA',
  description: '✨ Transformacyjne podróże do Azji 2025 - duchowe retreaty jogi na Bali i Sri Lanka. Odkryj siebie, medytacja, ayurveda, świątynie. Głęboka transformacja z Julią Jakubowicz.',
  keywords: 'transformacyjne podróże azja, duchowe podróże azja, transformacja osobista, spiritual journey, retreaty transformacyjne, duchowe wakacje, meditation retreat',
  openGraph: {
    title: 'Transformacyjne Podróże Azja 2025 - Duchowe Retreaty | BAKASANA',
    description: '✨ Transformacyjne podróże do Azji z duchowymi retreatami jogi. Odkryj siebie na Bali i Sri Lanka. Głęboka transformacja.',
    images: ['/images/og/transformacyjne-podroze-azja-2025.jpg'],
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog/transformacyjne-podroze-azja',
  },
};

const TransformacyjnePodrozeAzja = () => {
  const transformationAspects = [
    {
      icon: <Icon name="heart" size="lg" color="primary" />,
      title: "Emocjonalne uzdrowienie",
      description: "Uwolnij się od stresu i negatywnych emocji",
      color: "bg-pink-100"
    },
    {
      icon: <Sparkles className="w-6 h-6 text-charcoal" />,
      title: "Duchowe przebudzenie",
      description: "Połącz się z głębszą częścią siebie",
      color: "bg-purple-100"
    },
    {
      icon: <Zap className="w-6 h-6 text-charcoal" />,
      title: "Energetyczna odnowa",
      description: "Odnów swoją witalność i siłę życiową",
      color: "bg-yellow-100"
    },
    {
      icon: <Flower className="w-6 h-6 text-charcoal" />,
      title: "Mentalna jasność",
      description: "Osiągnij spokój umysłu i koncentrację",
      color: "bg-blue-100"
    }
  ];

  const transformationJourney = [
    {
      phase: "Przygotowanie",
      description: "Intencje, otwarcie serca, pierwszy kontakt z grupą",
      icon: <Sun className="w-5 h-5 text-charcoal" />,
      days: "Dni 1-2"
    },
    {
      phase: "Oczyszczenie",
      description: "Detoks ciała i umysłu, uwolnienie od nawyków",
      icon: <Waves className="w-5 h-5 text-charcoal" />,
      days: "Dni 3-5"
    },
    {
      phase: "Odkrywanie",
      description: "Głęboka praktyka, medytacja, praca z energią",
      icon: <Icon name="mountain" size="md" color="primary" />,
      days: "Dni 6-8"
    },
    {
      phase: "Integracja",
      description: "Połączenie doświadczeń, planowanie przyszłości",
      icon: <Moon className="w-5 h-5 text-charcoal" />,
      days: "Dni 9-10"
    }
  ];

  const spiritualPractices = [
    {
      name: "Sunrise Meditation",
      description: "Codzienna medytacja o wschodzie słońca",
      benefit: "Spokój umysłu",
      image: "/images/practices/sunrise-meditation.webp"
    },
    {
      name: "Mantra Chanting",
      description: "Śpiewanie świętych mantry",
      benefit: "Wibracja i energia",
      image: "/images/practices/mantra-chanting.webp"
    },
    {
      name: "Chakra Balancing",
      description: "Równoważenie centrów energetycznych",
      benefit: "Harmonia wewnętrzna",
      image: "/images/practices/chakra-balancing.webp"
    },
    {
      name: "Sacred Rituals",
      description: "Uczestnictwo w lokalnych ceremoniach",
      benefit: "Głęboka duchowość",
      image: "/images/practices/sacred-rituals.webp"
    }
  ];

  const testimonials = [
    {
      name: "Magdalena Kowalska",
      beforeAfter: {
        before: "Wypalona zawodowo, w stałym stresie",
        after: "Odnalazłam spokój i życiową pasję"
      },
      quote: "Ta podróż totalnie zmieniła moje życie. Wróciłam jako nowa osoba, pełna energii i radości.",
      transformation: "Emotional healing"
    },
    {
      name: "Tomasz Nowak",
      beforeAfter: {
        before: "Zagubiony, brak kierunku w życiu",
        after: "Jasne cele i głęboka motywacja"
      },
      quote: "Odkryłem kim naprawdę jestem. Azja nauczyła mnie być obecnym w chwili.",
      transformation: "Spiritual awakening"
    },
    {
      name: "Karolina Wiśniewska",
      beforeAfter: {
        before: "Problemy ze snem, lęki",
        after: "Spokój umysłu, pewność siebie"
      },
      quote: "Medytacja i joga w tak magicznych miejscach to była terapia na najwyższym poziomie.",
      transformation: "Mental clarity"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/spiritual/transformation-hero.webp"
            alt="Transformacyjne podróże Azja - duchowa przemiana"
            fill
            className="object-cover opacity-40"
            priority
          />
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-container-sm">
          <Badge className="mb-md bg-white/90 text-charcoal border-charcoal/30 px-hero-padding py-3 text-lg">
            ✨ Transformacyjne Podróże Azja 2025
          </Badge>
          
          <h1 className="text-5xl md:text-7xl font-bold text-charcoal mb-lg /* TODO: Replace with HeroTitle */">
            Odkryj <span className="text-charcoal">Siebie</span> <br />
            w Sercu Azji
          </h1>
          
          <p className="text-xl md:text-2xl text-wood mb-xl max-w-3xl mx-auto leading-relaxed /* TODO: Replace with SectionTitle */ /* TODO: Replace with CardTitle */">
            Duchowe retreaty jogi na Bali i Sri Lanka. Głęboka transformacja, 
            medytacja, ayurveda, świątynie. Podróż do siebie w najpiękniejszych 
            miejscach świata.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-md justify-center mb-xl">
            <Button 
              size="lg" 
              className="bg-charcoal hover:bg-charcoal/90 text-xl px-12 py-8"
              asChild
            >
              <Link href="/rezerwacja">
                Rozpocznij Transformację
                <Sparkles className="w-6 h-6 ml-3" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-charcoal text-charcoal hover:bg-charcoal/10 text-xl px-12 py-8"
              asChild
            >
              <Link href="/program">
                Odkryj Programy
              </Link>
            </Button>
          </div>
          
          <div className="flex flex-wrap items-center justify-center gap-lg text-wood">
            <div className="flex items-center gap-3">
              <Icon name="star" size="lg" color="accent" />
              <span className="text-lg">127 transformacji</span>
            </div>
            <div className="flex items-center gap-3">
              <Icon name="heart" size="lg" color="primary" />
              <span className="text-lg">10 lat doświadczenia</span>
            </div>
            <div className="flex items-center gap-3">
              <Flower className="w-6 h-6 text-charcoal" />
              <span className="text-lg">Duchowe przebudzenie</span>
            </div>
          </div>
        </div>
      </section>

      {/* Transformation Aspects */}
      <section className="py-section bg-gradient-to-br from-sanctuary to-whisper">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-2xl">
            <h2 className="text-4xl font-bold text-charcoal mb-md /* TODO: Replace with HeroTitle */">
              Obszary Transformacji
            </h2>
            <p className="text-xl text-wood max-w-3xl mx-auto">
              Nasze duchowe podróże oddziałują na wszystkie aspekty twojego bytu, 
              prowadząc do głębokiej i trwałej przemiany.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-lg">
            {transformationAspects.map((aspect, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20 hover:shadow-xl transition-shadow">
                <CardContent className="p-8 text-center">
                  <div className={`w-16 h-16 ${aspect.color} flex items-center justify-center mx-auto mb-md`}>
                    {aspect.icon}
                  </div>
                  <CardTitle>{aspect.title}</CardTitle>
                  <p className="text-wood">{aspect.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Transformation Journey */}
      <section className="py-section">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-2xl">
            <h2 className="text-4xl font-bold text-charcoal mb-md">
              Ścieżka Transformacji
            </h2>
            <p className="text-xl text-wood max-w-3xl mx-auto">
              Każda podróż to starannie zaplanowana ścieżka duchowego rozwoju 
              i osobistej przemiany.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-lg">
            {transformationJourney.map((phase, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20 relative">
                <CardContent className="p-8">
                  <div className="absolute -top-4 left-8 w-8 h-8 bg-charcoal flex items-center justify-center text-white font-bold">
                    {index + 1}
                  </div>
                  <div className="w-12 h-12 bg-charcoal/10 flex items-center justify-center mb-md mt-sm">
                    {phase.icon}
                  </div>
                  <Badge variant="secondary" className="mb-sm bg-charcoal/10 text-charcoal">
                    {phase.days}
                  </Badge>
                  <CardTitle>{phase.phase}</CardTitle>
                  <p className="text-wood">{phase.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Spiritual Practices */}
      <section className="py-section bg-charcoal/5">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-2xl">
            <h2 className="text-4xl font-bold text-charcoal mb-md">
              Duchowe Praktyki
            </h2>
            <p className="text-xl text-wood max-w-3xl mx-auto">
              Autentyczne praktyki duchowe Azji, które otwierają drzwi 
              do głębokiej transformacji i wewnętrznego pokoju.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-lg">
            {spiritualPractices.map((practice, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20 overflow-hidden">
                <div className="relative h-48">
                  <Image
                    src={practice.image}
                    alt={practice.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-charcoal mb-3">{practice.name}</h3>
                  <p className="text-wood text-sm mb-sm">{practice.description}</p>
                  <Badge className="bg-charcoal/10 text-charcoal">
                    {practice.benefit}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Transformation Stories */}
      <section className="py-section">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-2xl">
            <h2 className="text-4xl font-bold text-charcoal mb-md">
              Historie Transformacji
            </h2>
            <p className="text-xl text-wood max-w-3xl mx-auto">
              Prawdziwe historie uczestników, którzy doświadczyli 
              głębokiej przemiany podczas duchowych podróży.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-lg">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-charcoal/20">
                <CardContent className="p-8">
                  <div className="mb-md">
                    <CardTitle>{testimonial.name}</CardTitle>
                    <Badge className="bg-charcoal/10 text-charcoal text-xs">
                      {testimonial.transformation}
                    </Badge>
                  </div>
                  
                  <div className="space-y-sm mb-md">
                    <div className="p-4 bg-red-50">
                      <p className="text-sm text-red-700">
                        <strong>Przed:</strong> {testimonial.beforeAfter.before}
                      </p>
                    </div>
                    <div className="p-4 bg-green-50">
                      <p className="text-sm text-green-700">
                        <strong>Po:</strong> {testimonial.beforeAfter.after}
                      </p>
                    </div>
                  </div>
                  
                  <p className="text-wood italic">"{testimonial.quote}"</p>
                  
                  <div className="flex items-center gap-1 mt-sm">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-terra fill-golden" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Investment in Transformation */}
      <section className="py-section bg-sanctuary">
        <div className="max-w-7xl mx-auto px-container-sm">
          <div className="text-center mb-2xl">
            <h2 className="text-4xl font-bold text-charcoal mb-md">
              Inwestycja w Transformację
            </h2>
            <p className="text-xl text-wood max-w-3xl mx-auto">
              Duchowe podróże to inwestycja w siebie, która procentuje przez całe życie.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-xl">
            <Card className="bg-white/80 backdrop-blur-sm border-charcoal/20">
              <CardContent className="p-8">
                <div className="text-center mb-md">
                  <SectionTitle level={3}>Bali Transformation</SectionTitle>
                  <p className="text-wood">10 dni duchowej podróży</p>
                </div>
                
                <div className="space-y-sm mb-lg">
                  <div className="flex items-center gap-3">
                    <Icon name="check" size="md" color="primary" />
                    <span className="text-wood">Daily meditation & yoga</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Icon name="check" size="md" color="primary" />
                    <span className="text-wood">Świątynie i sacred sites</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Icon name="check" size="md" color="primary" />
                    <span className="text-wood">Ayurveda treatments</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Icon name="check" size="md" color="primary" />
                    <span className="text-wood">Spiritual guidance</span>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-charcoal mb-2 /* TODO: Replace with SectionTitle */">3400 PLN</div>
                  <Button className="w-full bg-charcoal hover:bg-charcoal/90" asChild>
                    <Link href="/rezerwacja?program=bali-transformation">
                      Rozpocznij Transformację
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/80 backdrop-blur-sm border-charcoal/20">
              <CardContent className="p-8">
                <div className="text-center mb-md">
                  <SectionTitle level={3}>Sri Lanka Awakening</SectionTitle>
                  <p className="text-wood">12 dni duchowego przebudzenia</p>
                </div>
                
                <div className="space-y-sm mb-lg">
                  <div className="flex items-center gap-3">
                    <Icon name="check" size="md" color="primary" />
                    <span className="text-wood">Sigiriya sunrise meditation</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Icon name="check" size="md" color="primary" />
                    <span className="text-wood">Buddhist temples</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Icon name="check" size="md" color="primary" />
                    <span className="text-wood">Authentic Ayurveda</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Icon name="check" size="md" color="primary" />
                    <span className="text-wood">Mountain meditation</span>
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-charcoal mb-2">3800 PLN</div>
                  <Button className="w-full bg-charcoal hover:bg-charcoal/90" asChild>
                    <Link href="/rezerwacja?program=srilanka-awakening">
                      Rozpocznij Przebudzenie
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-section bg-gradient-to-r from-charcoal/20 to-golden/20">
        <div className="max-w-4xl mx-auto px-container-sm text-center">
          <h2 className="text-4xl font-bold text-charcoal mb-md">
            Twoja Transformacja Zaczyna Się Teraz
          </h2>
          <p className="text-xl text-wood mb-xl max-w-2xl mx-auto">
            Nie czekaj na idealny moment. Ideal moment to teraz. 
            Dołącz do nas na transformacyjnej podróży do Azji.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-md justify-center mb-xl">
            <Button 
              size="lg" 
              className="bg-charcoal hover:bg-charcoal/90 text-xl px-12 py-8"
              asChild
            >
              <Link href="/rezerwacja">
                Zarezerwuj Transformację
                <Sparkles className="w-6 h-6 ml-3" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-charcoal text-charcoal hover:bg-charcoal/10 text-xl px-12 py-8"
              asChild
            >
              <Link href="/kontakt">
                Porozmawiajmy
              </Link>
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-lg text-wood">
            <div className="flex items-center gap-3">
              <Icon name="phone" size="md" color="primary" />
              <span>+48 666 777 888</span>
            </div>
            <div className="flex items-center gap-3">
              <Icon name="mail" size="md" color="primary" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-3">
              <Icon name="instagram" size="md" color="primary" />
              <span>@fly_with_bakasana</span>
            </div>
          </div>
        </div>
      </section>

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            "name": "Transformacyjne Podróże Azja",
            "description": "Duchowe retreaty jogi na Bali i Sri Lanka prowadzące do głębokiej transformacji osobistej",
            "provider": {
              "@type": "Organization",
              "name": "BAKASANA",
              "url": "https://bakasana-travel.blog"
            },
            "areaServed": {
              "@type": "Country",
              "name": "Poland"
            },
            "audience": {
              "@type": "Audience",
              "audienceType": "people seeking spiritual transformation"
            },
            "offers": {
              "@type": "Offer",
              "price": "3400",
              "priceCurrency": "PLN",
              "availability": "https://schema.org/InStock"
            }
          })
        }}
      />
    </div>
  );
};

export default TransformacyjnePodrozeAzja;