import { NextResponse } from 'next/server';

import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// Import bookings from parent route (w produkcji użyj bazy danych)
// Tymczasowe rozwiązanie - w rzeczywistości to powinno być w bazie danych
let bookings = [
  {
    id: '1',
    firstName: 'Anna',
    lastName: 'Kowal<PERSON>',
    email: '<EMAIL>',
    phone: '+48 123 456 789',
    program: 'Retreat Jogi',
    destination: 'Bali',
    status: 'pending',
    createdAt: new Date('2024-12-15T10:30:00Z').toISOString(),
    message: '<PERSON><PERSON><PERSON> się cieszę na ten retreat!'
  },
  {
    id: '2',
    firstName: 'Marcin',
    lastName: 'Nowak',
    email: '<EMAIL>',
    phone: '+48 987 654 321',
    program: '<PERSON>yu<PERSON><PERSON> & Joga',
    destination: 'Sri Lanka',
    status: 'confirmed',
    createdAt: new Date('2024-12-14T14:15:00Z').toISOString(),
    message: '<PERSON><PERSON> mogę dostać informacje o diecie?'
  },
  {
    id: '3',
    firstName: 'Katarzyna',
    lastName: 'Wiśniewska',
    email: '<EMAIL>',
    phone: '+48 555 123 456',
    program: 'Detox & Mindfulness',
    destination: 'Tajlandia',
    status: 'pending',
    createdAt: new Date('2024-12-13T09:45:00Z').toISOString(),
    message: 'Pierwszy raz na takim retrecie, jestem podekscytowana!'
  }
];

// Funkcja weryfikacji tokenu admin
function verifyAdminToken(request) {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { valid: false, error: 'Brak tokenu autoryzacji' };
  }

  const token = authHeader.substring(7);

  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'bakasana-travel-admin',
      audience: 'bakasana-travel-app'
    });

    if (decoded.role !== 'admin') {
      return { valid: false, error: 'Niewystarczające uprawnienia' };
    }

    return { valid: true, user: decoded };
  } catch (error) {
    return { valid: false, error: 'Nieprawidłowy token' };
  }
}

// GET - Pobierz pojedynczą rezerwację
export async function GET(request, { params }) {
  try {
    // Weryfikuj token admin
    const authResult = verifyAdminToken(request);
    if (!authResult.valid) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    const { id } = params;
    const booking = bookings.find(b => b.id === id);

    if (!booking) {
      return NextResponse.json(
        { success: false, error: 'Rezerwacja nie została znaleziona' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      booking
    });

  } catch (error) {
    console.error('Admin booking GET error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Błąd serwera podczas pobierania rezerwacji',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// PATCH - Aktualizuj status rezerwacji
export async function PATCH(request, { params }) {
  try {
    // Weryfikuj token admin
    const authResult = verifyAdminToken(request);
    if (!authResult.valid) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    const { id } = params;
    const updateData = await request.json();

    // Znajdź rezerwację
    const bookingIndex = bookings.findIndex(b => b.id === id);
    if (bookingIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Rezerwacja nie została znaleziona' },
        { status: 404 }
      );
    }

    // Walidacja statusu
    const validStatuses = ['pending', 'confirmed', 'cancelled'];
    if (updateData.status && !validStatuses.includes(updateData.status)) {
      return NextResponse.json(
        { success: false, error: 'Nieprawidłowy status rezerwacji' },
        { status: 400 }
      );
    }

    // Aktualizuj rezerwację
    const oldStatus = bookings[bookingIndex].status;
    bookings[bookingIndex] = {
      ...bookings[bookingIndex],
      ...updateData,
      updatedAt: new Date().toISOString(),
      updatedBy: authResult.user.ip || 'admin'
    };

    // Log zmiany statusu
    if (updateData.status && updateData.status !== oldStatus) {
      console.log(`Booking ${id} status changed from ${oldStatus} to ${updateData.status} by admin`);
    }

    return NextResponse.json({
      success: true,
      booking: bookings[bookingIndex],
      message: 'Rezerwacja została zaktualizowana pomyślnie'
    });

  } catch (error) {
    console.error('Admin booking PATCH error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Błąd serwera podczas aktualizacji rezerwacji',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// DELETE - Usuń rezerwację
export async function DELETE(request, { params }) {
  try {
    // Weryfikuj token admin
    const authResult = verifyAdminToken(request);
    if (!authResult.valid) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    const { id } = params;
    const bookingIndex = bookings.findIndex(b => b.id === id);

    if (bookingIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Rezerwacja nie została znaleziona' },
        { status: 404 }
      );
    }

    // Usuń rezerwację
    const deletedBooking = bookings.splice(bookingIndex, 1)[0];

    console.log(`Booking ${id} deleted by admin`);

    return NextResponse.json({
      success: true,
      message: 'Rezerwacja została usunięta pomyślnie',
      deletedBooking
    });

  } catch (error) {
    console.error('Admin booking DELETE error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Błąd serwera podczas usuwania rezerwacji',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
