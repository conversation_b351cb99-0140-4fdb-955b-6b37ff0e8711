#!/usr/bin/env node

/**
 * BAKASANA - UNIFIED DESIGN SYSTEM MIGRATION SCRIPT
 * Automatyczna migracja do nowego systemu designu
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Kolory do migracji
const COLOR_MIGRATIONS = {
  'text-temple': 'text-charcoal',
  'text-temple-gold': 'text-enterprise-brown',
  'text-golden': 'text-terra',
  'text-wood-light': 'text-charcoal-light',
  'bg-temple': 'bg-charcoal',
  'bg-temple-gold': 'bg-enterprise-brown',
  'bg-golden': 'bg-terra',
  'bg-rice': 'bg-sanctuary',
  'bg-mist': 'bg-linen',
  'border-temple': 'border-charcoal',
  'border-temple-gold': 'border-enterprise-brown',
  'border-golden': 'border-terra',
  'hover:bg-temple': 'hover:bg-charcoal',
  'hover:text-temple': 'hover:text-charcoal',
  'hover:text-golden': 'hover:text-terra'
};

// Ikony do migracji (Heroicons → Lucide)
const ICON_MIGRATIONS = {
  'MapPinIcon': 'map-pin',
  'ClockIcon': 'clock',
  'UsersIcon': 'users',
  'StarIcon': 'star',
  'CheckIcon': 'check',
  'ArrowRightIcon': 'arrow-right',
  'CalendarIcon': 'calendar',
  'PhoneIcon': 'phone',
  'MailIcon': 'mail',
  'InstagramIcon': 'instagram',
  'HeartIcon': 'heart',
  'AwardIcon': 'award',
  'MountainIcon': 'mountain',
  'MapPin': 'map-pin',
  'Clock': 'clock',
  'Users': 'users',
  'Star': 'star',
  'Check': 'check',
  'ArrowRight': 'arrow-right',
  'Calendar': 'calendar',
  'Phone': 'phone',
  'Mail': 'mail',
  'Instagram': 'instagram',
  'Heart': 'heart',
  'Award': 'award',
  'Mountain': 'mountain'
};

class UnifiedMigrator {
  constructor() {
    this.srcPath = path.join(__dirname, '../src');
    this.migratedFiles = [];
    this.errors = [];
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m'
    };
    
    console.log(`${colors[type]}[${type.toUpperCase()}]${colors.reset} ${message}`);
  }

  async migrateColors() {
    this.log('🎨 Migracja kolorów...', 'info');
    
    const files = this.getAllFiles(this.srcPath, ['.jsx', '.tsx', '.js', '.ts']);
    
    for (const file of files) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let modified = false;

        for (const [oldColor, newColor] of Object.entries(COLOR_MIGRATIONS)) {
          const regex = new RegExp(`\\b${oldColor}\\b`, 'g');
          if (regex.test(content)) {
            content = content.replace(regex, newColor);
            modified = true;
          }
        }

        if (modified) {
          fs.writeFileSync(file, content);
          this.migratedFiles.push(file);
          this.log(`✓ ${path.relative(this.srcPath, file)}`, 'success');
        }
      } catch (error) {
        this.errors.push(`Color migration error in ${file}: ${error.message}`);
        this.log(`✗ ${path.relative(this.srcPath, file)}: ${error.message}`, 'error');
      }
    }
  }

  async migrateIcons() {
    this.log('🎯 Migracja ikon...', 'info');
    
    const files = this.getAllFiles(this.srcPath, ['.jsx', '.tsx']);
    
    for (const file of files) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let modified = false;

        // Sprawdź czy plik używa ikon
        const hasHeroicons = content.includes('@heroicons/react');
        const hasLucide = content.includes('lucide-react');
        
        if (hasHeroicons || hasLucide) {
          // Dodaj import Icon system tylko jeśli nie ma błędów składniowych
          if (!content.includes("import { Icon } from '@/components/ui/IconSystem'") && 
              !content.includes("import.*from.*import")) {
            // Znajdź ostatni poprawny import
            const lines = content.split('\n');
            let lastImportIndex = -1;
            
            for (let i = 0; i < lines.length; i++) {
              if (lines[i].trim().startsWith('import ') && 
                  lines[i].includes('from ') && 
                  lines[i].includes(';')) {
                lastImportIndex = i;
              }
            }
            
            if (lastImportIndex >= 0) {
              lines.splice(lastImportIndex + 1, 0, "import { Icon } from '@/components/ui/IconSystem';");
              content = lines.join('\n');
              modified = true;
            }
          }

          // Zamień użycia ikon
          for (const [oldIcon, newIconName] of Object.entries(ICON_MIGRATIONS)) {
            // Pattern dla <IconName className="..." />
            const iconPattern = new RegExp(
              `<${oldIcon}\\s+className="([^"]*)"\\s*/>`,
              'g'
            );
            
            content = content.replace(iconPattern, (match, className) => {
              const sizeMatch = className.match(/w-(\d+)\s+h-\d+/);
              const colorMatch = className.match(/text-([a-z-]+)/);
              
              let size = 'md';
              if (sizeMatch) {
                const sizeNum = parseInt(sizeMatch[1]);
                if (sizeNum <= 3) size = 'xs';
                else if (sizeNum <= 4) size = 'sm';
                else if (sizeNum <= 5) size = 'md';
                else if (sizeNum <= 6) size = 'lg';
                else size = 'xl';
              }

              let color = 'primary';
              if (colorMatch) {
                const colorName = colorMatch[1];
                if (colorName.includes('temple') || colorName.includes('enterprise')) color = 'accent';
                else if (colorName.includes('golden') || colorName.includes('terra')) color = 'accent';
              }

              modified = true;
              return `<Icon name="${newIconName}" size="${size}" color="${color}" />`;
            });
          }

          // Usuń stare importy ikon
          content = content.replace(/import\s*{[^}]*}\s*from\s*['"]@heroicons\/react\/24\/outline['"];?\s*/g, '');
          content = content.replace(/import\s*{[^}]*}\s*from\s*['"]lucide-react['"];?\s*/g, '');
        }

        if (modified) {
          fs.writeFileSync(file, content);
          this.migratedFiles.push(file);
          this.log(`✓ ${path.relative(this.srcPath, file)}`, 'success');
        }
      } catch (error) {
        this.errors.push(`Icon migration error in ${file}: ${error.message}`);
        this.log(`✗ ${path.relative(this.srcPath, file)}: ${error.message}`, 'error');
      }
    }
  }

  async migrateButtons() {
    this.log('🔘 Migracja przycisków...', 'info');
    
    const files = this.getAllFiles(this.srcPath, ['.jsx', '.tsx']);
    
    for (const file of files) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let modified = false;

        // Dodaj import UnifiedButton jeśli potrzebny
        if (content.includes('<button') && !content.includes('UnifiedButton')) {
          const importMatch = content.match(/^(import.*from.*['"][^'"]*['"];?\s*)+/m);
          if (importMatch) {
            const lastImport = importMatch[0];
            content = content.replace(
              lastImport,
              lastImport + "import { UnifiedButton } from '@/components/ui/UnifiedButton';\n"
            );
            modified = true;
          }
        }

        // Zamień proste buttony na UnifiedButton
        const buttonPattern = /<button\s+className="([^"]*)"([^>]*)>(.*?)<\/button>/gs;
        content = content.replace(buttonPattern, (match, className, attrs, children) => {
          // Określ wariant na podstawie klas
          let variant = 'primary';
          if (className.includes('border') && !className.includes('bg-')) {
            variant = 'secondary';
          } else if (className.includes('bg-transparent')) {
            variant = 'ghost';
          }

          // Określ rozmiar
          let size = 'md';
          if (className.includes('px-12') || className.includes('py-4')) {
            size = 'lg';
          } else if (className.includes('px-6') || className.includes('py-2')) {
            size = 'sm';
          }

          modified = true;
          return `<UnifiedButton variant="${variant}" size="${size}"${attrs}>${children}</UnifiedButton>`;
        });

        if (modified) {
          fs.writeFileSync(file, content);
          this.migratedFiles.push(file);
          this.log(`✓ ${path.relative(this.srcPath, file)}`, 'success');
        }
      } catch (error) {
        this.errors.push(`Button migration error in ${file}: ${error.message}`);
        this.log(`✗ ${path.relative(this.srcPath, file)}: ${error.message}`, 'error');
      }
    }
  }

  getAllFiles(dir, extensions) {
    let files = [];
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files = files.concat(this.getAllFiles(fullPath, extensions));
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
    
    return files;
  }

  async generateReport() {
    this.log('\n📊 Raport migracji:', 'info');
    this.log(`✅ Zmigrowane pliki: ${this.migratedFiles.length}`, 'success');
    
    if (this.errors.length > 0) {
      this.log(`❌ Błędy: ${this.errors.length}`, 'error');
      this.errors.forEach(error => this.log(`  - ${error}`, 'error'));
    }

    // Zapisz raport
    const report = {
      timestamp: new Date().toISOString(),
      migratedFiles: this.migratedFiles.map(f => path.relative(this.srcPath, f)),
      errors: this.errors,
      summary: {
        totalFiles: this.migratedFiles.length,
        totalErrors: this.errors.length
      }
    };

    fs.writeFileSync(
      path.join(__dirname, '../migration-report.json'),
      JSON.stringify(report, null, 2)
    );

    this.log('📄 Raport zapisany w migration-report.json', 'info');
  }

  async run() {
    this.log('🚀 Rozpoczynam migrację do Unified Design System...', 'info');
    
    try {
      await this.migrateColors();
      await this.migrateIcons();
      await this.migrateButtons();
      await this.generateReport();
      
      this.log('\n🎉 Migracja zakończona pomyślnie!', 'success');
      this.log('📋 Sprawdź STYLE_GUIDE.md dla dalszych instrukcji', 'info');
      
    } catch (error) {
      this.log(`💥 Błąd podczas migracji: ${error.message}`, 'error');
      process.exit(1);
    }
  }
}

// Uruchom migrację
if (require.main === module) {
  const migrator = new UnifiedMigrator();
  migrator.run();
}

module.exports = UnifiedMigrator;