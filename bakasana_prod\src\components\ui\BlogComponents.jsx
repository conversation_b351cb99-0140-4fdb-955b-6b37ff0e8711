'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { Icon } from './IconSystem';

/**
 * 🎨 BAKASANA - UNIFIED BLOG COMPONENTS
 * Consistent styling for blog posts using brand colors
 */

/**
 * Alert Box Component - replaces colored bg-* classes with brand colors
 */
export function AlertBox({ 
  type = 'info', 
  title, 
  children, 
  icon,
  className = '',
  ...props 
}) {
  const alertStyles = {
    info: {
      container: 'bg-warm-enterprise/5 border-warm-enterprise/20 text-charcoal',
      title: 'text-warm-enterprise',
      icon: 'info'
    },
    warning: {
      container: 'bg-warm-terra/5 border-warm-terra/20 text-charcoal',
      title: 'text-warm-terra',
      icon: 'alert-triangle'
    },
    success: {
      container: 'bg-warm-enterprise/5 border-warm-enterprise/20 text-charcoal',
      title: 'text-warm-enterprise',
      icon: 'check-circle'
    },
    error: {
      container: 'bg-red-50 border-red-200 text-charcoal',
      title: 'text-red-700',
      icon: 'alert-circle'
    }
  };

  const styles = alertStyles[type];
  const iconName = icon || styles.icon;

  return (
    <div 
      className={cn(
        'rounded-xl p-6 my-6 border',
        styles.container,
        className
      )}
      {...props}
    >
      {title && (
        <div className="flex items-center gap-3 mb-3">
          <Icon 
            name={iconName} 
            size="md" 
            className={styles.title}
          />
          <h4 className={cn('text-lg font-semibold', styles.title)}>
            {title}
          </h4>
        </div>
      )}
      <div className="text-charcoal-light">
        {children}
      </div>
    </div>
  );
}

/**
 * Info Box - for informational content
 */
export function InfoBox({ title, children, ...props }) {
  return (
    <AlertBox 
      type="info" 
      title={title} 
      icon="info"
      {...props}
    >
      {children}
    </AlertBox>
  );
}

/**
 * Warning Box - for warnings and cautions
 */
export function WarningBox({ title, children, ...props }) {
  return (
    <AlertBox 
      type="warning" 
      title={title} 
      icon="alert-triangle"
      {...props}
    >
      {children}
    </AlertBox>
  );
}

/**
 * Success Box - for positive information
 */
export function SuccessBox({ title, children, ...props }) {
  return (
    <AlertBox 
      type="success" 
      title={title} 
      icon="check-circle"
      {...props}
    >
      {children}
    </AlertBox>
  );
}

/**
 * Error Box - for errors and critical information
 */
export function ErrorBox({ title, children, ...props }) {
  return (
    <AlertBox 
      type="error" 
      title={title} 
      icon="alert-circle"
      {...props}
    >
      {children}
    </AlertBox>
  );
}

/**
 * Highlight Box - for special content highlighting
 */
export function HighlightBox({ 
  title, 
  children, 
  icon = 'sparkles',
  className = '',
  ...props 
}) {
  return (
    <div 
      className={cn(
        'bg-warm-gold/5 border border-warm-gold/20 p-6 my-6',
        className
      )}
      {...props}
    >
      {title && (
        <div className="flex items-center gap-3 mb-3">
          <Icon 
            name={icon} 
            size="md" 
            className="text-warm-gold"
          />
          <h4 className="text-lg font-semibold text-warm-gold">
            {title}
          </h4>
        </div>
      )}
      <div className="text-charcoal-light">
        {children}
      </div>
    </div>
  );
}

/**
 * Quote Box - for testimonials and quotes
 */
export function QuoteBox({ 
  quote, 
  author, 
  role,
  className = '',
  ...props 
}) {
  return (
    <div 
      className={cn(
        'bg-sanctuary border-l-4 border-warm-enterprise rounded-r-xl p-6 my-6',
        'shadow-warm-subtle',
        className
      )}
      {...props}
    >
      <div className="flex items-start gap-3">
        <Icon 
          name="quote" 
          size="lg" 
          className="text-warm-enterprise mt-1 flex-shrink-0"
        />
        <div>
          <blockquote className="text-charcoal-light text-lg italic leading-relaxed mb-sm">
            "{quote}"
          </blockquote>
          {author && (
            <div className="text-right">
              <cite className="text-warm-enterprise font-medium not-italic">
                {author}
              </cite>
              {role && (
                <div className="text-sage text-sm mt-1">
                  {role}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Step Box - for step-by-step instructions
 */
export function StepBox({ 
  step, 
  title, 
  children,
  className = '',
  ...props 
}) {
  return (
    <div 
      className={cn(
        'bg-sanctuary border border-warm-enterprise/20 p-6 my-4',
        'shadow-warm-subtle',
        className
      )}
      {...props}
    >
      <div className="flex items-start gap-sm">
        <div className="flex-shrink-0 w-8 h-8 bg-warm-enterprise text-sanctuary flex items-center justify-center font-semibold text-sm">
          {step}
        </div>
        <div className="flex-1">
          {title && (
            <h4 className="text-lg font-semibold text-charcoal mb-2">
              {title}
            </h4>
          )}
          <div className="text-charcoal-light">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Feature List - for listing features or benefits
 */
export function FeatureList({ items, className = '', ...props }) {
  return (
    <div className={cn('space-y-3 my-6', className)} {...props}>
      {items.map((item, index) => (
        <div key={index} className="flex items-start gap-3">
          <Icon 
            name="check" 
            size="sm" 
            className="text-warm-enterprise mt-1 flex-shrink-0"
          />
          <span className="text-charcoal-light">{item}</span>
        </div>
      ))}
    </div>
  );
}

/**
 * Pros and Cons - for comparison content
 */
export function ProsAndCons({ pros = [], cons = [], className = '', ...props }) {
  return (
    <div className={cn('grid md:grid-cols-2 gap-md my-6', className)} {...props}>
      {pros.length > 0 && (
        <div className="bg-warm-enterprise/5 border border-warm-enterprise/20 p-6">
          <h4 className="flex items-center gap-2 text-lg font-semibold text-warm-enterprise mb-sm">
            <Icon name="check-circle" size="md" />
            Zalety
          </h4>
          <ul className="space-y-2">
            {pros.map((pro, index) => (
              <li key={index} className="flex items-start gap-2">
                <Icon name="plus" size="sm" className="text-warm-enterprise mt-1 flex-shrink-0" />
                <span className="text-charcoal-light">{pro}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {cons.length > 0 && (
        <div className="bg-red-50 border border-red-200 p-6">
          <h4 className="flex items-center gap-2 text-lg font-semibold text-red-700 mb-sm">
            <Icon name="x-circle" size="md" />
            Wady
          </h4>
          <ul className="space-y-2">
            {cons.map((con, index) => (
              <li key={index} className="flex items-start gap-2">
                <Icon name="minus" size="sm" className="text-red-600 mt-1 flex-shrink-0" />
                <span className="text-charcoal-light">{con}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

/**
 * Call to Action Box - for CTAs in blog posts
 */
export function CTABox({ 
  title, 
  description, 
  buttonText, 
  buttonHref,
  icon = 'arrow-right',
  className = '',
  ...props 
}) {
  return (
    <div 
      className={cn(
        'bg-gradient-to-r from-warm-enterprise/5 to-warm-terra/5',
        'border border-warm-enterprise/20 p-8 my-8 text-center',
        'shadow-warm-elegant',
        className
      )}
      {...props}
    >
      {title && (
        <h3 className="text-2xl font-cormorant font-light text-charcoal mb-sm /* TODO: Replace with SectionTitle */">
          {title}
        </h3>
      )}
      {description && (
        <p className="text-charcoal-light mb-md max-w-2xl mx-auto">
          {description}
        </p>
      )}
      {buttonText && buttonHref && (
        <a 
          href={buttonHref}
          className="inline-flex items-center gap-2 bg-warm-enterprise text-sanctuary px-hero-padding py-3 rounded-none hover:bg-warm-terra transition-all duration-300 font-light uppercase tracking-wide"
        >
          {buttonText}
          <Icon name={icon} size="sm" />
        </a>
      )}
    </div>
  );
}

export default {
  AlertBox,
  InfoBox,
  WarningBox,
  SuccessBox,
  ErrorBox,
  HighlightBox,
  QuoteBox,
  StepBox,
  FeatureList,
  ProsAndCons,
  CTABox
};