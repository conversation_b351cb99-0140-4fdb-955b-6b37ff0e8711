#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Mapowanie starych kolorów na nowe
const colorMappings = {
  // Legacy colors → New unified colors
  'bg-shell': 'bg-silk',
  'text-shell': 'text-silk',
  'border-shell': 'border-silk',
  
  'bg-temple': 'bg-charcoal',
  'text-temple': 'text-charcoal',
  'border-temple': 'border-charcoal',
  
  'bg-temple-gold': 'bg-enterprise-brown',
  'text-temple-gold': 'text-enterprise-brown',
  'border-temple-gold': 'border-enterprise-brown',
  
  'bg-golden': 'bg-sand',
  'text-golden': 'text-sand',
  'border-golden': 'border-sand',
  
  'bg-golden-lotus': 'bg-enterprise-brown',
  'text-golden-lotus': 'text-enterprise-brown',
  'border-golden-lotus': 'border-enterprise-brown',
  
  'bg-golden-amber': 'bg-terra',
  'text-golden-amber': 'text-terra',
  'border-golden-amber': 'border-terra',
  
  'bg-wood-light': 'bg-charcoal-light',
  'text-wood-light': 'text-charcoal-light',
  'border-wood-light': 'border-charcoal-light',
  
  'bg-rice': 'bg-sanctuary',
  'text-rice': 'text-sanctuary',
  'border-rice': 'border-sanctuary',
  
  'bg-mist': 'bg-whisper',
  'text-mist': 'text-whisper',
  'border-mist': 'border-whisper',
  
  // Focus ring colors
  'focus:ring-temple/20': 'focus:ring-enterprise-brown/20',
  'focus:ring-temple-gold/20': 'focus:ring-enterprise-brown/20',
  
  // Hover colors
  'hover:bg-temple': 'hover:bg-charcoal',
  'hover:text-temple': 'hover:text-charcoal',
  'hover:bg-temple-gold': 'hover:bg-enterprise-brown',
  'hover:text-temple-gold': 'hover:text-enterprise-brown',
  
  // Shadow colors
  'shadow-temple': 'shadow-charcoal',
  'shadow-temple-gold': 'shadow-enterprise-brown',
  
  // Gradient colors
  'from-rice': 'from-sanctuary',
  'to-mist': 'to-whisper',
  'from-temple': 'from-charcoal',
  'to-temple-gold': 'to-enterprise-brown'
};

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extensions) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do naprawy legacy colors
function fixLegacyColors(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  Object.entries(colorMappings).forEach(([oldColor, newColor]) => {
    if (content.includes(oldColor)) {
      const regex = new RegExp(oldColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      content = content.replace(regex, newColor);
      changed = true;
    }
  });
  
  if (changed) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed legacy colors in: ${filePath}`);
    return true;
  }
  
  return false;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = findFiles(srcDir, ['.jsx', '.tsx', '.js', '.ts', '.css']);
  
  console.log(`🔍 Found ${files.length} files to check for legacy colors...`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixLegacyColors(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🎉 Fixed legacy colors in ${fixedCount} files!`);
  console.log('✨ All colors now use unified BAKASANA color system.');
}

main();