'use client';

import React, { memo } from 'react';
import PerformantWhatsApp from './PerformantWhatsApp';

// Elegant CTA box for blog posts
const BlogWhatsAppCTA = memo(({ 
  category = 'Joga',
  postTitle = '',
  className = ''
}) => {
  
  // Generate contextual message based on post content
  const generateContextualMessage = () => {
    const baseMessage = 'Cześć Julia! ';
    
    if (postTitle.toLowerCase().includes('bali')) {
      return `${baseMessage}Przeczytałam/em artykuł "${postTitle}" i jestem zainteresowana/y retreatami na Bali. Czy możesz przesłać mi więcej informacji?`;
    }
    
    if (postTitle.toLowerCase().includes('sri lanka')) {
      return `${baseMessage}Przeczytałam/em artykuł "${postTitle}" i jestem zainteresowana/y retreatami na Sri Lance. <PERSON><PERSON> moż<PERSON>z przesłać mi więcej informacji?`;
    }
    
    if (category === 'Joga Zaawansowana') {
      return `${baseMessage}Przeczytałam/em artykuł "${postTitle}" i chciałabym/chciałbym pogłębić swoją praktykę jogi. Opowiedz mi o swoich retreatach!`;
    }
    
    if (category === 'Medytacja') {
      return `${baseMessage}Artykuł "${postTitle}" bardzo mnie zainspirował. Szukam miejsca na duchowy rozwój - czy Twoje retreaty to dobry wybór?`;
    }
    
    return `${baseMessage}Przeczytałam/em artykuł "${postTitle}" i jestem zainteresowana/y Twoimi retreatami. Czy możesz przesłać więcej informacji?`;
  };

  return (
    <div className={`
      bg-gradient-to-br from-shell/60 to-whisper/40 
      backdrop-blur-sm border border-charcoal/10 
      p-8 my-12 text-center
      shadow-soft hover:shadow-medium transition-all duration-300
      ${className}
    `}>
      {/* Elegant header */}
      <div className="mb-md">
        <h3 className="text-2xl font-cormorant text-charcoal mb-3 /* TODO: Replace with SectionTitle */">
          Porozmawiajmy o Twojej podróży
        </h3>
        <p className="text-charcoal-light/80 leading-relaxed max-w-md mx-auto">
          Masz pytania o retreaty? Chcesz poznać szczegóły? 
          Napisz do mnie na WhatsApp - odpowiem osobiście.
        </p>
      </div>

      {/* Decorative divider */}
      <div className="flex items-center justify-center mb-md">
        <div className="w-8 h-0.5 bg-charcoal/20"></div>
        <div className="w-2 h-2 bg-charcoal/30 mx-3"></div>
        <div className="w-8 h-0.5 bg-charcoal/20"></div>
      </div>

      {/* WhatsApp CTA Button */}
      <PerformantWhatsApp
        variant="ghost"
        contextualMessage={generateContextualMessage()}
        className="mx-auto"
      />

      {/* Trust indicator */}
      <div className="mt-md text-xs text-charcoal-light/60">
        <span>✓ Odpowiadam osobiście</span>
        <span className="mx-3">•</span>
        <span>✓ Bez zobowiązań</span>
        <span className="mx-3">•</span>
        <span>✓ Szybka odpowiedź</span>
      </div>
    </div>
  );
});

BlogWhatsAppCTA.displayName = 'BlogWhatsAppCTA';

export default BlogWhatsAppCTA;