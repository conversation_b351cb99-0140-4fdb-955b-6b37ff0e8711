#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extension) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Pomijamy node_modules i .next
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extension));
      }
    } else if (file.endsWith(extension)) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do naprawy rounded elements
function fixRoundedElements(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  // Lista wszystkich rounded classes do usunięcia
  const roundedClasses = [
    'rounded-full',
    'rounded-2xl',
    'rounded-xl', 
    'rounded-lg',
    'rounded-md',
    'rounded-sm',
    'rounded'
  ];
  
  roundedClasses.forEach(roundedClass => {
    const regex = new RegExp(`\\s${roundedClass}(?=\\s|"|')`, 'g');
    if (content.includes(roundedClass)) {
      content = content.replace(regex, '');
      changed = true;
    }
  });
  
  if (changed) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed rounded elements in: ${filePath}`);
    return true;
  }
  
  return false;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = [
    ...findFiles(srcDir, '.jsx'),
    ...findFiles(srcDir, '.tsx'),
    ...findFiles(srcDir, '.js'),
    ...findFiles(srcDir, '.ts')
  ];
  
  console.log(`🔍 Found ${files.length} files to check...`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (fixRoundedElements(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🎉 Fixed rounded elements in ${fixedCount} files!`);
  console.log('✨ All elements now follow BAKASANA rectangular design system.');
}

main();