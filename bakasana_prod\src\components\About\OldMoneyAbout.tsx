'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

const OldMoneyAbout: React.FC = () => {
  return (
    <section className="relative py-section-lg bg-sanctuary">
      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="h-full w-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%238B7355' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="container relative">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            
            {/* Left: Photo */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative overflow-hidden">
                {/* Elegant frame effect */}
                <div className="absolute inset-0 border border-enterprise-brown/10" />
                
                <Image 
                  src="/images/gallery/IMG_6624.jpg" 
                  alt="Julia - Instruktorka Jogi"
                  width={600}
                  height={600}
                  className="w-full h-[600px] object-cover filter grayscale-[20%]"
                  priority
                />
                
                {/* Subtle overlay gradient */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent" />
              </div>
              
              {/* Decorative element */}
              <div className="absolute -bottom-6 -right-6 w-32 h-32 border border-enterprise-brown/20" />
            </motion.div>

            {/* Right: Content */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Small badge */}
              <span className="inline-block text-xs uppercase tracking-[3px] text-enterprise-brown mb-lg">
                O mnie
              </span>

              {/* Main content */}
              <h2 className="font-cormorant text-5xl font-light text-charcoal mb-lg leading-tight /* TODO: Replace with HeroTitle */">
                Cześć, jestem Julia
              </h2>

              <div className="space-y-md text-charcoal-light leading-relaxed">
                <p className="text-lg">
                  Certyfikowana instruktorka jogi i fizjoterapeutka. Prowadzę 
                  retreaty na Bali i Sri Lance, które łączą intensywną praktykę 
                  jogi z odkrywaniem duchowego bogactwa Azji.
                </p>

                <p>
                  Moja przygoda z jogą rozpoczęła się 10 lat temu podczas 
                  pierwszej podróży do Indii. Od tego czasu praktykowałam 
                  z najlepszymi nauczycielami w Rishikesh, Mysore i Ubud.
                </p>

                <p>
                  Wierzę, że joga to nie tylko praktyka na macie, ale sposób 
                  życia. W moich retreatach łączę tradycyjną hatha jogę 
                  z elementami vinyasy, pranayamy i medytacji.
                </p>
              </div>

              {/* Credentials */}
              <div className="mt-10 pt-10 border-t border-silk">
                <div className="grid grid-cols-2 gap-lg">
                  <div>
                    <div className="text-3xl font-light text-enterprise-brown mb-2 /* TODO: Replace with SectionTitle */">
                      500h
                    </div>
                    <div className="text-xs uppercase tracking-widest text-ash">
                      Yoga Alliance RYT
                    </div>
                  </div>
                  <div>
                    <div className="text-3xl font-light text-enterprise-brown mb-2">
                      10+
                    </div>
                    <div className="text-xs uppercase tracking-widest text-ash">
                      Lat doświadczenia
                    </div>
                  </div>
                </div>
              </div>

              {/* CTA */}
              <motion.a
                href="/o-mnie"
                className="inline-flex items-center mt-xl text-sm tracking-wider text-enterprise-brown hover:text-terra transition-all duration-500 group"
              >
                Poznaj moją historię
                <svg className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </motion.a>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OldMoneyAbout;