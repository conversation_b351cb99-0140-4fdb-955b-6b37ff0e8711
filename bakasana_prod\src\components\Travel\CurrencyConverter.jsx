'use client';

import React, { useState, useEffect } from 'react';
import { 
  ArrowUpDownIcon, 
  RefreshCwIcon, 
  TrendingUpIcon, 
  TrendingDownIcon,
  InfoIcon,
  CreditCardIcon
} from 'import { Icon } from '@/components/ui/IconSystem';
lucide-react';

import { UnifiedButton } from '@/components/ui/UnifiedButton';
const CurrencyConverter = ({ 
  baseCurrency = 'EUR',
  targetCurrency = 'USD',
  amount = 1,
  compact = false,
  className = '' 
}) => {
  const [fromCurrency, setFromCurrency] = useState(baseCurrency);
  const [toCurrency, setToCurrency] = useState(targetCurrency);
  const [inputAmount, setInputAmount] = useState(amount);
  const [convertedAmount, setConvertedAmount] = useState(0);
  const [exchangeRate, setExchangeRate] = useState(1);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [rates, setRates] = useState({});
  const [trend, setTrend] = useState(null);

  // Supported currencies for travel
  const currencies = [
    { code: 'EUR', name: 'Euro', symbol: '€', flag: '🇪🇺' },
    { code: 'USD', name: 'US Dollar', symbol: '$', flag: '🇺🇸' },
    { code: 'PLN', name: 'Polish Złoty', symbol: 'zł', flag: '🇵🇱' },
    { code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp', flag: '🇮🇩' },
    { code: 'LKR', name: 'Sri Lankan Rupee', symbol: 'Rs', flag: '🇱🇰' },
    { code: 'GBP', name: 'British Pound', symbol: '£', flag: '🇬🇧' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥', flag: '🇯🇵' },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', flag: '🇦🇺' },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', flag: '🇨🇦' },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'Fr', flag: '🇨🇭' }
  ];

  // Mock exchange rates (in real app, fetch from API)
  const mockRates = {
    EUR: {
      USD: 1.09,
      PLN: 4.33,
      IDR: 16800,
      LKR: 355,
      GBP: 0.86,
      JPY: 163,
      AUD: 1.66,
      CAD: 1.49,
      CHF: 0.96
    },
    USD: {
      EUR: 0.92,
      PLN: 3.97,
      IDR: 15400,
      LKR: 325,
      GBP: 0.79,
      JPY: 150,
      AUD: 1.52,
      CAD: 1.37,
      CHF: 0.88
    },
    PLN: {
      EUR: 0.23,
      USD: 0.25,
      IDR: 3558,
      LKR: 82,
      GBP: 0.20,
      JPY: 37.6,
      AUD: 0.38,
      CAD: 0.34,
      CHF: 0.22
    },
    IDR: {
      EUR: 0.000060,
      USD: 0.000065,
      PLN: 0.00028,
      LKR: 0.021,
      GBP: 0.000051,
      JPY: 0.0097,
      AUD: 0.000099,
      CAD: 0.000089,
      CHF: 0.000057
    },
    LKR: {
      EUR: 0.0028,
      USD: 0.0031,
      PLN: 0.012,
      IDR: 47.3,
      GBP: 0.0024,
      JPY: 0.46,
      AUD: 0.0047,
      CAD: 0.0042,
      CHF: 0.0027
    }
  };

  // Price comparison data for common expenses
  const priceComparisons = {
    'IDR': {
      'meal': { local: 25000, tourist: 75000, fine: 200000 },
      'coffee': { local: 15000, tourist: 35000, fine: 60000 },
      'transport': { local: 5000, tourist: 25000, fine: 100000 },
      'accommodation': { budget: 200000, mid: 600000, luxury: 1500000 }
    },
    'LKR': {
      'meal': { local: 500, tourist: 1500, fine: 4000 },
      'coffee': { local: 200, tourist: 600, fine: 1000 },
      'transport': { local: 100, tourist: 500, fine: 2000 },
      'accommodation': { budget: 3000, mid: 10000, luxury: 30000 }
    },
    'EUR': {
      'meal': { local: 2, tourist: 8, fine: 25 },
      'coffee': { local: 1, tourist: 3, fine: 6 },
      'transport': { local: 0.5, tourist: 3, fine: 15 },
      'accommodation': { budget: 15, mid: 50, luxury: 150 }
    }
  };

  useEffect(() => {
    fetchExchangeRates();
  }, [fromCurrency, toCurrency]);

  useEffect(() => {
    calculateConversion();
  }, [inputAmount, exchangeRate]);

  const fetchExchangeRates = async () => {
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const baseRates = mockRates[fromCurrency] || {};
      const rate = baseRates[toCurrency] || 1;
      
      setExchangeRate(rate);
      setRates(baseRates);
      setLastUpdated(new Date());
      
      // Simulate trend (random for demo)
      const randomTrend = Math.random() > 0.5 ? 'up' : 'down';
      setTrend(randomTrend);
      
    } catch (error) {
      console.error('Failed to fetch exchange rates:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateConversion = () => {
    const result = inputAmount * exchangeRate;
    setConvertedAmount(result);
  };

  const swapCurrencies = () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
    setInputAmount(convertedAmount);
  };

  const formatCurrency = (amount, currencyCode) => {
    const currency = currencies.find(c => c.code === currencyCode);
    if (!currency) return amount.toFixed(2);
    
    const options = {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: currencyCode === 'IDR' || currencyCode === 'LKR' ? 0 : 2,
      maximumFractionDigits: currencyCode === 'IDR' || currencyCode === 'LKR' ? 0 : 2
    };
    
    try {
      return new Intl.NumberFormat('en-US', options).format(amount);
    } catch {
      return `${currency.symbol}${amount.toFixed(2)}`;
    }
  };

  const getCurrencyInfo = (code) => {
    return currencies.find(c => c.code === code);
  };

  if (compact) {
    return (
      <div className={`bg-white rectangular p-4 shadow-sm ${className}`}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-medium text-charcoal">Currency</h3>
          <button
            onClick={fetchExchangeRates}
            disabled={loading}
            className="text-charcoal-gold hover:text-charcoal-gold/80 transition-colors"
          >
            <RefreshCwIcon className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          </button>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="text-sm">
            <span className="text-2xl font-bold text-charcoal /* TODO: Replace with SectionTitle */">1</span>
            <span className="text-stone ml-1">{fromCurrency}</span>
          </div>
          
          <div className="text-stone">=</div>
          
          <div className="text-sm">
            <span className="text-xl font-bold text-charcoal /* TODO: Replace with CardTitle */">
              {formatCurrency(exchangeRate, toCurrency)}
            </span>
          </div>
        </div>
        
        <div className="text-xs text-stone mt-2">
          Updated: {lastUpdated.toLocaleTimeString()}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rectangular shadow-sm overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 bg-gradient-to-r from-charcoal-gold/5 to-sage-green/5 border-b border-stone-light">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-cormorant font-medium text-charcoal /* TODO: Replace with CardTitle */">
              Currency Converter
            </h3>
            <p className="text-sm text-stone">
              Live exchange rates for travel planning
            </p>
          </div>
          
          <button
            onClick={fetchExchangeRates}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-1 bg-charcoal-gold/10 text-charcoal-gold hover:bg-charcoal-gold/20 transition-colors"
          >
            <RefreshCwIcon className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span className="text-sm">Refresh</span>
          </button>
        </div>
      </div>

      {/* Converter */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-sm items-end">
          {/* From Currency */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-charcoal">From</label>
            <div className="relative">
              <select
                value={fromCurrency}
                onChange={(e) => setFromCurrency(e.target.value)}
                className="w-full px-3 py-2 border border-stone-light rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold appearance-none bg-white"
              >
                {currencies.map(currency => (
                  <option key={currency.code} value={currency.code}>
                    {currency.flag} {currency.code} - {currency.name}
                  </option>
                ))}
              </select>
            </div>
            <input
              type="number"
              value={inputAmount}
              onChange={(e) => setInputAmount(parseFloat(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-stone-light rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold"
              placeholder="Enter amount"
            />
          </div>

          {/* Swap Button */}
          <div className="flex justify-center mb-lg">
            <button
              onClick={swapCurrencies}
              className="p-2 bg-charcoal-gold/10 text-charcoal-gold rectangular hover:bg-charcoal-gold/20 transition-colors"
            >
              <ArrowUpDownIcon className="w-5 h-5" />
            </button>
          </div>

          {/* To Currency */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-charcoal">To</label>
            <div className="relative">
              <select
                value={toCurrency}
                onChange={(e) => setToCurrency(e.target.value)}
                className="w-full px-3 py-2 border border-stone-light rectangular focus:outline-none focus:ring-2 focus:ring-temple-gold-gold-gold appearance-none bg-white"
              >
                {currencies.map(currency => (
                  <option key={currency.code} value={currency.code}>
                    {currency.flag} {currency.code} - {currency.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="w-full px-3 py-2 bg-sanctuary rectangular text-charcoal font-medium">
              {formatCurrency(convertedAmount, toCurrency)}
            </div>
          </div>
        </div>

        {/* Exchange Rate Info */}
        <div className="mt-md p-4 bg-sanctuary rectangular">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <span className="text-sm text-stone">Exchange Rate:</span>
              <span className="font-medium text-charcoal">
                1 {fromCurrency} = {formatCurrency(exchangeRate, toCurrency)}
              </span>
            </div>
            
            {trend && (
              <div className={`flex items-center gap-1 text-sm ${
                trend === 'up' ? 'text-green-600' : 'text-charcoal-gold'
              }`}>
                {trend === 'up' ? (
                  <TrendingUpIcon className="w-4 h-4" />
                ) : (
                  <TrendingDownIcon className="w-4 h-4" />
                )}
                <span>{trend === 'up' ? 'Rising' : 'Falling'}</span>
              </div>
            )}
          </div>
          
          <div className="text-xs text-stone">
            Last updated: {lastUpdated.toLocaleString()}
          </div>
        </div>
      </div>

      {/* Price Comparison */}
      {priceComparisons[toCurrency] && (
        <div className="p-6 border-t border-stone-light">
          <h4 className="font-cormorant font-medium text-charcoal mb-sm">
            Price Guide in {getCurrencyInfo(toCurrency)?.name}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-sm">
            {Object.entries(priceComparisons[toCurrency]).map(([category, prices]) => (
              <div key={category} className="bg-sanctuary p-4 rectangular">
                <h5 className="font-medium text-charcoal mb-2 capitalize">
                  {category}
                </h5>
                <div className="space-y-1">
                  {Object.entries(prices).map(([level, price]) => (
                    <div key={level} className="flex justify-between text-sm">
                      <span className="text-stone capitalize">{level}:</span>
                      <span className="font-medium text-charcoal">
                        {formatCurrency(price, toCurrency)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Travel Tips */}
      <div className="p-6 border-t border-stone-light bg-sage-green/5">
        <div className="flex items-center gap-2 mb-3">
          <CreditCardIcon className="w-5 h-5 text-charcoal-gold" />
          <h4 className="font-cormorant font-medium text-charcoal">
            Money Tips for Travel
          </h4>
        </div>
        
        <div className="space-y-2 text-sm text-stone">
          <div className="flex items-start gap-2">
            <InfoIcon className="w-4 h-4 mt-0.5 text-charcoal-gold" />
            <span>
              Notify your bank before traveling to avoid card blocks
            </span>
          </div>
          
          <div className="flex items-start gap-2">
            <InfoIcon className="w-4 h-4 mt-0.5 text-charcoal-gold" />
            <span>
              Carry small bills for local markets and tips
            </span>
          </div>
          
          <div className="flex items-start gap-2">
            <InfoIcon className="w-4 h-4 mt-0.5 text-charcoal-gold" />
            <span>
              Use ATMs at banks for better exchange rates
            </span>
          </div>
          
          <div className="flex items-start gap-2">
            <InfoIcon className="w-4 h-4 mt-0.5 text-charcoal-gold" />
            <span>
              Keep some cash in USD/EUR as backup
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CurrencyConverter;