{"timestamp": "2025-07-22T08:14:45.302Z", "stats": {"totalFiles": 199, "checkedFiles": 199, "issues": 615, "score": 0}, "issues": [{"file": "app\\admin\\bookings\\page.jsx", "line": 108, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\bookings\\page.jsx", "line": 159, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\bookings\\page.jsx", "line": 231, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\bookings\\page.jsx", "line": 132, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\admin\\page.jsx", "line": 83, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 109, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 116, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 123, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 168, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 183, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 193, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 208, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 218, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 231, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 241, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 254, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 264, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 277, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 290, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 294, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 298, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 302, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\admin\\page.jsx", "line": 95, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\admin\\page.jsx", "line": 95, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\admin\\page.jsx", "line": 146, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 97, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 106, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 36, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 36, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 51, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 51, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\co-zabrac-na-joge-bali-lista\\page.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 72, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 187, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 193, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 34, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 34, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 49, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 49, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 180, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\blog\\ile-kosztuje-retreat-jogi-na-bali-2025\\page.jsx", "line": 180, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 154, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 99, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 113, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 113, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 129, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 144, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 144, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 169, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 7, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 10, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 11, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\blog\\[slug]\\page.jsx", "line": 12, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\error.jsx", "line": 28, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\error.jsx", "line": 31, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\galeria\\page.jsx", "line": 101, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\galeria\\page.jsx", "line": 101, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\galeria\\page.jsx", "line": 138, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\galeria\\page.jsx", "line": 138, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 123, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 212, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 239, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 321, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 339, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 343, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 347, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 173, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 203, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 238, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 246, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 262, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 270, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 280, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 288, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 105, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 160, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 189, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 231, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\joga-sri-lanka-retreat\\page.jsx", "line": 305, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 37, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 43, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 136, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 141, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 145, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 224, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 337, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 345, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 355, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 363, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 122, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 211, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 243, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 287, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 381, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\juli<PERSON>-j<PERSON><PERSON><PERSON><PERSON>-instruktor\\page.jsx", "line": 421, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\kontakt\\page.jsx", "line": 71, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\kontakt\\page.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\kontakt\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 7, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 8, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 9, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 10, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 11, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 12, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 13, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\layout.jsx", "line": 15, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\mapa\\page.jsx", "line": 41, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\mapa\\page.jsx", "line": 47, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\mapa\\page.jsx", "line": 53, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\mapa\\page.jsx", "line": 59, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\mapa\\page.jsx", "line": 16, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\mapa\\page.jsx", "line": 16, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\mapa\\page.jsx", "line": 31, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\mapa\\page.jsx", "line": 69, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\mapa\\page.jsx", "line": 109, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\mapa\\page.jsx", "line": 109, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\mapa\\page.jsx", "line": 145, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\mapa\\page.jsx", "line": 145, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\mapa\\page.jsx", "line": 195, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\mapa\\page.jsx", "line": 195, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\mapa\\page.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\not-found-bali.jsx", "line": 48, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\not-found-bali.jsx", "line": 83, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\not-found-bali.jsx", "line": 19, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\not-found-bali.jsx", "line": 32, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\old-money\\page.tsx", "line": 2, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\old-money\\page.tsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\old-money\\page.tsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\old-money\\page.tsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\old-money\\page.tsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\page.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\page.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\page.jsx", "line": 7, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 36, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 112, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 141, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 145, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 149, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 36, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 73, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 80, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 92, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 112, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 130, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 141, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 145, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 149, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 163, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 167, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 171, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 180, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 15, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 15, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 31, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 31, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 47, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 47, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 71, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 71, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 91, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 91, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 102, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 102, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 121, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 121, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 139, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 139, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 157, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 157, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 179, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 179, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 195, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\polityka-prywatnosci\\page.jsx", "line": 195, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\page.jsx", "line": 450, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 530, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 571, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 678, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 679, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\program\\page.jsx", "line": 73, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 82, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 286, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 323, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 418, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 450, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 511, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 530, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 552, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 571, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 678, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 679, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\program\\page.jsx", "line": 431, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\page.jsx", "line": 516, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\page.jsx", "line": 557, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 106, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 106, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 130, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 130, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 144, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 168, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 168, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 181, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 195, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 195, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 221, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 246, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 246, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 259, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 259, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 291, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 311, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\program\\srilanka\\page.jsx", "line": 311, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\retreaty\\page.jsx", "line": 136, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty\\page.jsx", "line": 112, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty\\page.jsx", "line": 155, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty\\page.jsx", "line": 169, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty\\page.jsx", "line": 212, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty\\page.jsx", "line": 257, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty\\page.jsx", "line": 318, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 159, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 182, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 189, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 196, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 226, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 234, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 244, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 252, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 91, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 146, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 176, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 270, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\retreaty-jogi-bali-2025\\page.jsx", "line": 308, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\rezerwacja\\page.jsx", "line": 41, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\rezerwacja\\page.jsx", "line": 47, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\rezerwacja\\page.jsx", "line": 53, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\rezerwacja\\page.jsx", "line": 59, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\rezerwacja\\page.jsx", "line": 137, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\rezerwacja\\page.jsx", "line": 21, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\rezerwacja\\page.jsx", "line": 21, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\rezerwacja\\page.jsx", "line": 36, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\rezerwacja\\page.jsx", "line": 68, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\rezerwacja\\page.jsx", "line": 125, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\rezerwacja\\page.jsx", "line": 125, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "app\\rezerwacja\\page.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\rezerwacja\\page.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\rezerwacja\\page.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\rezerwacja\\page.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 31, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 37, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 43, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 54, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 60, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 72, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 172, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 198, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 466, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 222, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 251, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 254, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 331, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 336, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 153, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 209, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 238, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 273, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 310, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 361, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 373, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 410, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\transformacyjne-podroze-azja\\page.jsx", "line": 450, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 25, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 35, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 166, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 189, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 193, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 334, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 338, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 431, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 514, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 218, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 409, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 416, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 423, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 430, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 437, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 454, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 462, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 472, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 480, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 147, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 205, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 234, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 262, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 308, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 367, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 403, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "app\\yoga-retreat-z-polski\\page.jsx", "line": 498, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\About\\OldMoneyAbout.tsx", "line": 46, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\About\\OldMoneyAbout.tsx", "line": 63, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\About\\OldMoneyAbout.tsx", "line": 2, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\AnimatedCounter.jsx", "line": 80, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\BlogWhatsAppCTA.jsx", "line": 58, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\BlogWhatsAppCTA.jsx", "line": 46, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\BlogWhatsAppCTA.jsx", "line": 46, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 437, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 505, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 855, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 870, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 887, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 897, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 907, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 955, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 957, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 989, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 874, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Booking\\EnhancedBookingFlow.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\BookingCalendar.jsx", "line": 142, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\BookingCalendar.jsx", "line": 142, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\BookingCalendar.jsx", "line": 192, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\BookingCalendar.jsx", "line": 218, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\BookingCalendar.jsx", "line": 218, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\BookingForm.jsx", "line": 454, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\BookingForm.jsx", "line": 454, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\ClientInteractiveButton.jsx", "line": 47, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ClientInteractiveButton.jsx", "line": 59, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ErrorBoundary\\AdvancedErrorBoundary.jsx", "line": 92, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Events\\EventCard.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Events\\EventCard.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\FAQSection.jsx", "line": 87, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "components\\FAQSection.jsx", "line": 70, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\FAQSection.jsx", "line": 70, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\FAQSection.jsx", "line": 138, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\Footer\\OldMoneyFooter.tsx", "line": 2, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Footer\\OldMoneyFooter.tsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Footer\\OldMoneyFooter.tsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Footer\\ServerFooter.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\GhostNavbar.jsx", "line": 184, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\GhostNavbar.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\GhostNavbar.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Hero\\OldMoneyHero.tsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\HighlightCard\\index.jsx", "line": 13, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\HighlightCard\\index.jsx", "line": 10, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\Home\\CustomColorHero.jsx", "line": 83, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\Home\\CustomColorHero.jsx", "line": 91, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\Home\\CustomColorHero.jsx", "line": 102, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\Home\\CustomColorHero.jsx", "line": 135, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\Home\\HeroVariantsDemo.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Home\\ProfessionalHero.jsx", "line": 41, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Home\\WellnessPage.jsx", "line": 8, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\InteractiveMap.jsx", "line": 173, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\InteractiveMap.jsx", "line": 173, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\InteractiveMap.jsx", "line": 259, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\InteractiveMap.jsx", "line": 291, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\InteractiveMap.jsx", "line": 291, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\InternalLinks.jsx", "line": 57, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "components\\InternalLinks.jsx", "line": 107, "type": "legacy-color", "message": "Legacy color \"bg-shell\" found", "suggestion": "Replace with \"bg-silk\""}, {"file": "components\\InternalLinks.jsx", "line": 48, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\InternalLinks.jsx", "line": 97, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\InternalLinks.jsx", "line": 97, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\InternalLinks.jsx", "line": 120, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\MinimalistHero.jsx", "line": 103, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\MinimalistHero.jsx", "line": 104, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\MinimalistHero.jsx", "line": 105, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\MinimalistHero.jsx", "line": 225, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\MinimalistHero.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\MinimalistHero.jsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\MinimalistHero.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Navbar\\ClientNavbar.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 233, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 253, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 288, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 298, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 356, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 383, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 413, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 447, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 491, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 256, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 361, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 488, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Navigation\\EnterpriseNavbar.tsx", "line": 9, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 71, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 118, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 119, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 120, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 5, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Navigation\\OldMoneyNavbar.tsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\NewsletterSignup.jsx", "line": 87, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\NewsletterSignup.jsx", "line": 87, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\NewsletterSignup.jsx", "line": 137, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\NewsletterSignup.jsx", "line": 137, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\NewsletterSignup.jsx", "line": 162, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\NewsletterSignup.jsx", "line": 162, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\NewsletterSignup.jsx", "line": 206, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\PerfectNavbar.jsx", "line": 109, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 132, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 156, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 241, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 247, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 253, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 261, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerfectNavbar.jsx", "line": 6, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 353, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 357, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 368, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 375, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 382, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 426, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 451, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 482, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 498, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 509, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 520, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 616, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 626, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 636, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 679, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 689, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 699, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 763, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 348, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 357, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 366, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 373, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 380, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 405, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 425, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 431, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 445, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 496, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 507, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 518, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 549, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 571, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 589, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 614, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 624, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 634, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 645, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 677, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 687, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 697, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 713, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 718, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 728, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 730, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 741, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 749, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\EnterprisePerformanceDashboard.tsx", "line": 763, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 28, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 58, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 131, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 132, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 133, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 174, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 175, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 177, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 178, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 181, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 182, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 185, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 186, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 198, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 200, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 201, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 206, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 207, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 219, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 235, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 239, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Performance\\LoadingStates.jsx", "line": 244, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PerformantWhatsApp.jsx", "line": 97, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PerformantWhatsApp.jsx", "line": 87, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PerformantWhatsApp.jsx", "line": 112, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 378, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 380, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 382, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 384, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 391, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 393, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 395, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 418, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 443, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 484, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 500, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 520, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 525, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 579, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 586, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 629, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 666, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 684, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 746, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 285, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 378, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 417, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 451, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 498, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 514, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 566, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 577, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 584, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 595, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 628, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 691, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 760, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\PWA\\EnterprisePWAInstaller.tsx", "line": 487, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 217, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 219, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 221, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 223, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 262, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 264, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 318, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 360, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 364, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 368, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 372, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\PWA\\PWAInstall.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\PWAInstaller.jsx", "line": 234, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\RetreatCalendar.jsx", "line": 20, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\SanityRetreats.jsx", "line": 171, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\SanityRetreats.jsx", "line": 229, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\SanityRetreats.jsx", "line": 229, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\SanityRetreats.jsx", "line": 267, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\SanityRetreats.jsx", "line": 267, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\SanityTestimonials.jsx", "line": 76, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\SanityTestimonials.jsx", "line": 115, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\SanityTestimonials.jsx", "line": 115, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\SanityTestimonials.jsx", "line": 139, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\SanityTestimonials.jsx", "line": 143, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\SanityTestimonials.jsx", "line": 147, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\SanityTestimonials.jsx", "line": 151, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\ScrollReveal.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 100, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 152, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 152, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 2, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Services\\OldMoneyServices.tsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\TestimonialSlider.jsx", "line": 51, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\TestimonialSlider.jsx", "line": 53, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\TestimonialSlider.jsx", "line": 89, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\TestimonialSlider.jsx", "line": 91, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\TransformationCTA.jsx", "line": 14, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 288, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 329, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 331, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 376, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 384, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 391, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 398, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 405, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\CurrencyConverter.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Travel\\DestinationCard.jsx", "line": 157, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\DestinationCard.jsx", "line": 165, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\DestinationCard.jsx", "line": 4, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Travel\\InteractiveMap.jsx", "line": 215, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\InteractiveMap.jsx", "line": 218, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 153, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 166, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 179, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 195, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 384, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 421, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\TravelGuide.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 142, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 144, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 145, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 156, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 230, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 234, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 238, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 249, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 255, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 261, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 299, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 322, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 329, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 336, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\Travel\\WeatherWidget.jsx", "line": 342, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\TrustBadges.jsx", "line": 35, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\TrustBadges.jsx", "line": 35, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\TrustBadges.jsx", "line": 46, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\ui\\BlogComponents.jsx", "line": 245, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\ui\\BlogComponents.jsx", "line": 349, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\ui\\ElegantQuote.jsx", "line": 42, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\ui\\ElegantQuote.jsx", "line": 56, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\ui\\EnhancedButton.jsx", "line": 172, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\LazyImage.jsx", "line": 143, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\OptimizedLazyImage.jsx", "line": 191, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\Section.jsx", "line": 129, "type": "hardcoded-icon", "message": "Hardcoded icon size/color found", "suggestion": "Use <Icon name=\"...\" size=\"md\" color=\"accent\" /> instead"}, {"file": "components\\ui\\Section.jsx", "line": 106, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "components\\ui\\Section.jsx", "line": 106, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\ui\\Section.jsx", "line": 139, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\WebVitals.jsx", "line": 3, "type": "import-order", "message": "Imports are not in correct order", "suggestion": "Order: React/Next → UI Components → Custom Components → Data/Utils"}, {"file": "components\\WorldClassDesign\\OpticalTypography.jsx", "line": 111, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\WorldClassDesign\\OpticalTypography.jsx", "line": 118, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\WorldClassDesign\\OpticalTypography.jsx", "line": 125, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\WorldClassDesign\\OpticalTypography.jsx", "line": 146, "type": "font-family", "message": "Generic font-serif found", "suggestion": "Use font-cormorant for headings"}, {"file": "components\\WorldClassDesign\\SmartPreloader.jsx", "line": 155, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\WorldClassDesign\\SmartPreloader.jsx", "line": 156, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\WorldClassDesign\\SmartPreloader.jsx", "line": 157, "type": "rounded-elements", "message": "Rounded elements found", "suggestion": "BAKASANA uses rectangular design (border-radius: 0)"}, {"file": "components\\WorldClassDesign\\SmartPreloader.jsx", "line": 127, "type": "hardcoded-typography", "message": "Hardcoded heading styles found", "suggestion": "Use HeroTitle, SectionTitle, or CardTitle components"}, {"file": "data\\blogPosts.js", "line": 1, "type": "icon-system", "message": "File uses legacy icon imports", "suggestion": "Migrate to unified Icon system"}, {"file": "data\\contactData.js", "line": 1, "type": "icon-system", "message": "File uses legacy icon imports", "suggestion": "Migrate to unified Icon system"}, {"file": "data\\eventData.js", "line": 1, "type": "icon-system", "message": "File uses legacy icon imports", "suggestion": "Migrate to unified Icon system"}], "summary": {"score": 0, "totalIssues": 615, "issuesByType": {"rounded-elements": 163, "font-family": 93, "hardcoded-typography": 105, "legacy-color": 12, "import-order": 67, "hardcoded-icon": 172, "icon-system": 3}}}