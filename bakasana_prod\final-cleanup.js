#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Funkcja do rekurencyjnego przeszukiwania plików
function findFiles(dir, extensions) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      if (!['node_modules', '.next', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else if (extensions.some(ext => file.endsWith(ext))) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Funkcja do agresywnej naprawy wszystkich problemów
function aggressiveCleanup(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let changed = false;
  
  // 1. Usuń WSZYSTKIE rounded classes
  const roundedClasses = [
    'rounded-full', 'rounded-2xl', 'rounded-xl', 'rounded-lg', 
    'rounded-md', 'rounded-sm', 'rounded'
  ];
  
  roundedClasses.forEach(roundedClass => {
    const regex = new RegExp(`\\s${roundedClass}(?=\\s|"|')`, 'g');
    if (content.includes(roundedClass)) {
      content = content.replace(regex, '');
      changed = true;
    }
  });
  
  // 2. Zamień wszystkie hardcoded headings na placeholder (do manual review)
  const hardcodedHeadings = [
    // h1 z dużymi rozmiarami
    { pattern: /className="([^"]*text-4xl[^"]*)"/, replacement: 'className="$1 /* TODO: Replace with HeroTitle */"' },
    { pattern: /className="([^"]*text-5xl[^"]*)"/, replacement: 'className="$1 /* TODO: Replace with HeroTitle */"' },
    { pattern: /className="([^"]*text-6xl[^"]*)"/, replacement: 'className="$1 /* TODO: Replace with HeroTitle */"' },
    
    // h2, h3 z średnimi rozmiarami
    { pattern: /className="([^"]*text-3xl[^"]*)"/, replacement: 'className="$1 /* TODO: Replace with SectionTitle */"' },
    { pattern: /className="([^"]*text-2xl[^"]*)"/, replacement: 'className="$1 /* TODO: Replace with SectionTitle */"' },
    
    // h3, h4 z małymi rozmiarami
    { pattern: /className="([^"]*text-xl[^"]*)"/, replacement: 'className="$1 /* TODO: Replace with CardTitle */"' },
    { pattern: /className="([^"]*text-lg[^"]*font-medium[^"]*)"/, replacement: 'className="$1 /* TODO: Replace with CardTitle */"' }
  ];
  
  hardcodedHeadings.forEach(mapping => {
    if (mapping.pattern.test(content)) {
      content = content.replace(mapping.pattern, mapping.replacement);
      changed = true;
    }
  });
  
  // 3. Usuń wszystkie pozostałe problemy ze spacingiem
  const spacingFixes = {
    'py-20': 'py-section',
    'py-24': 'py-section',
    'py-32': 'py-section-lg',
    'px-6': 'px-hero-padding',
    'px-8': 'px-hero-padding',
    'mb-8': 'mb-lg',
    'mb-6': 'mb-md',
    'gap-8': 'gap-lg',
    'gap-6': 'gap-md'
  };
  
  Object.entries(spacingFixes).forEach(([old, newVal]) => {
    if (content.includes(old)) {
      const regex = new RegExp(`\\b${old}\\b`, 'g');
      content = content.replace(regex, newVal);
      changed = true;
    }
  });
  
  if (changed) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Cleaned up: ${filePath}`);
    return true;
  }
  
  return false;
}

// Główna funkcja
function main() {
  const srcDir = path.join(__dirname, 'src');
  const files = findFiles(srcDir, ['.jsx', '.tsx', '.js', '.ts']);
  
  console.log(`🧹 FINAL AGGRESSIVE CLEANUP - BAKASANA`);
  console.log(`Found ${files.length} files to clean...`);
  
  let fixedCount = 0;
  files.forEach(file => {
    if (aggressiveCleanup(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\n🎉 Cleaned up ${fixedCount} files!`);
  console.log('✨ All major style inconsistencies have been addressed.');
  console.log('📝 Files with TODO comments need manual review for typography.');
}

main();