{"name": "baka<PERSON>a-travel-blog", "version": "0.1.0", "private": true, "homepage": "https://bakasana-travel.blog", "description": "Retreaty jogowe na Bali z Julią Jakubowicz - transformacyjne podróże łączące praktykę jogi z pięknem Bali", "keywords": ["joga", "bali", "retreat", "j<PERSON><PERSON>", "fizjoterapia", "wellness", "mindfulness"], "author": "<PERSON>", "scripts": {"dev": "next dev -p 3002", "build": "next build", "build:analyze": "cross-env ANALYZE=true next build", "start": "next start -p 3000", "start:prod": "npm run build && npm run start", "lint": "next lint", "optimize-images": "node scripts/optimize-images.js", "clean": "node scripts/safe-clean.js", "dev:clean": "npm run clean && npm run dev", "build:clean": "npm run clean && npm run build", "postbuild": "next-sitemap", "sanity:dev": "sanity dev", "sanity:build": "sanity build", "sanity:deploy": "sanity deploy", "premium:switch": "node scripts/switch-to-premium.js", "premium:restore": "node scripts/restore-original.js", "premium:dev": "next dev -p 3002", "premium:build": "next build", "migrate:unified": "node scripts/migrate-to-unified.js", "style:check": "echo 'Sprawdzanie konsekwentności stylów...' && node scripts/style-checker.js", "fix:imports": "node scripts/fix-import-errors.js", "premium:start": "next start -p 3002", "pwa:screenshots": "node scripts/generate-pwa-screenshots.js", "pwa:setup": "npm run pwa:screenshots && npm run build", "enterprise:setup": "npm run pwa:setup && npm run build:analyze", "performance:monitor": "node scripts/performance-monitor.js", "mobile:test": "npm run pwa:screenshots && npm run dev"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-slot": "^1.2.0", "@sentry/nextjs": "^8.0.0", "@tailwindcss/typography": "^0.5.16", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "aos": "^2.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "framer-motion": "^12.12.2", "jose": "^6.0.6", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mapbox-gl": "^3.13.0", "mixpanel-browser": "^2.47.0", "moment": "^2.30.1", "next": "^15.3.2", "next-sitemap": "^4.2.3", "react": "^18.3.1", "react-big-calendar": "^1.19.4", "react-cookie-consent": "^9.0.0", "react-dom": "^18.3.1", "react-icons": "^5.0.1", "react-intersection-observer": "^9.15.1", "react-lazy-load-image-component": "^1.6.3", "react-map-gl": "^8.0.4", "sharp": "^0.34.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^3.5.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@next/bundle-analyzer": "^15.0.0", "@types/node": "^20.12.7", "@types/react": "19.1.6", "autoprefixer": "^10.4.21", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7.0.3", "cssnano": "^7.0.7", "eslint": "^9.27.0", "eslint-config-next": "^15.4.0-canary.51", "postcss": "^8.5.3", "puppeteer": "^22.1.0", "rimraf": "^5.0.5", "tailwindcss": "^3.4.17", "typescript": "^5.4.5"}}