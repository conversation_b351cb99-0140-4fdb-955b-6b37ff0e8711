'use client';

import Image from 'next/image';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';
import {  useResponsiveStyles, getHeroStyles, getSectionStyles  } from '@/components/ui/UnifiedButton';
import {  UnifiedButton  } from '@/components/ui/UnifiedButton';

export default function ZajeciaOnlinePage() {
  const { isDesktop, isTablet, isMobile } = useResponsiveStyles();
  const heroStyles = getHeroStyles(isDesktop, isTablet, isMobile);
  const sectionStyles = getSectionStyles(isDesktop, isTablet, isMobile);
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Zajęcia Jogi Online - Julia Jakubowicz",
    "description": "Prywatne lekcje jogi online i grupowe zajęcia online z certyfikowaną instruktorką jogi RYT 500.",
    "provider": {
      "@type": "Person",
      "name": "<PERSON>"
    }
  };

  // Dane sekc<PERSON> zgodnie z opisem wizualnym
  const privateClasses = {
    title: "Praktyka indywidualna",
    subtitle: "Osobista przestrzeń do odkrywania głębi praktyki jogi w zaciszu własnego domu.",
    description: "Każda sesja jest starannie dostosowana do Twojego poziomu zaawansowania, obecnych potrzeb ciała i ducha. Wspólnie tworzymy przestrzeń, w której możesz bezpiecznie eksplorować różne aspekty jogi - od asany po pranayamę i medytację.",
    features: [
      "Wstępna rozmowa o intencjach i celach",
      "Program stworzony specjalnie dla Ciebie",
      "Delikatne korekty w czasie rzeczywistym",
      "Nagranie sesji do kontynuacji praktyki",
      "Wsparcie między sesjami"
    ],
    offerings: [
      { name: "Konsultacja", duration: "30 min", note: "Poznanie i ustalenie celów" },
      { name: "Sesja indywidualna", duration: "60 min", note: "Pełna praktyka dostosowana do Ciebie" },
      { name: "Cykl 4 sesji", duration: "60 min", note: "Pogłębienie i rozwój praktyki" },
      { name: "Cykl 8 sesji", duration: "60 min", note: "Transformacja i stabilizacja" }
    ]
  };

  const groupClasses = {
    title: "Praktyka grupowa",
    subtitle: "Energia wspólnoty połączona z indywidualną uwagą w małych, intymnych grupach.",
    description: "Praktykuj w towarzystwie innych, czerpiąc siłę z energii grupy, jednocześnie otrzymując personalną uwagę. Każda osoba jest widziana i wspierana w swojej unikalnej podróży.",
    schedule: [
      { day: "Poniedziałek", time: "18:00", type: "Hatha Yoga", level: "dla początkujących" },
      { day: "Środa", time: "19:00", type: "Vinyasa Flow", level: "dla praktykujących" },
      { day: "Piątek", time: "17:30", type: "Yin Yoga", level: "dla wszystkich" },
      { day: "Sobota", time: "10:00", type: "Yoga Terapeutyczna", level: "dla ciała i duszy" }
    ],
    offerings: [
      { name: "Małe grupy", size: "4-6 osób", note: "Intymna atmosfera" },
      { name: "Średnie grupy", size: "7-12 osób", note: "Energia wspólnoty" },
      { name: "Miesięczny cykl", size: "8 praktyk", note: "Regularna praktyka" },
      { name: "Kwartalny cykl", size: "24 praktyki", note: "Głęboka transformacja" }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <main className="bg-sanctuary min-h-screen">
        {/* HERO SECTION - Magazine Style Header */}
        <section className="magazine-hero">
          <div className="magazine-hero-content">
            <div className="magazine-header-line"></div>
            
            <h1 className="magazine-title">
              Online
            </h1>

            <p className="magazine-subtitle">
              Praktyka jogi w Twojej przestrzeni
            </p>

            <div className="magazine-meta">
              Sesje indywidualne i grupowe
            </div>
            
            <div className="magazine-header-line"></div>
          </div>
        </section>

        {/* SEKCJA INDYWIDUALNE - Dwukolumnowa */}
        <section style={sectionStyles.section}>
          <div style={sectionStyles.grid}>
            {/* Kolumna tekstowa (lewa) */}
            <div>
              <h2 style={sectionStyles.sectionTitle}>
                {privateClasses.title}
              </h2>

              <p style={{
                fontSize: '17px',
                fontFamily: 'Inter',
                fontWeight: 300,
                color: '#5B5754',
                lineHeight: '1.8',
                marginBottom: '32px'
              }}>
                {privateClasses.subtitle}
              </p>

              <div style={{
                fontSize: '15px',
                fontFamily: 'Inter',
                fontWeight: 300,
                color: '#3D3A37',
                lineHeight: '1.9',
                letterSpacing: '0.02em',
                marginBottom: '56px',
                textAlign: 'left'
              }}>
                {privateClasses.description}
              </div>

              <div style={{marginBottom: '64px'}}>
                <h3 style={{
                  fontSize: '20px',
                  fontFamily: 'Cormorant Garamond',
                  fontWeight: 400,
                  color: '#3D3A37',
                  marginBottom: '28px'
                }}>
                  Struktura sesji
                </h3>
                
                <div>
                  {privateClasses.features.map((feature, index) => (
                    <div key={index} style={{
                      padding: '16px 0',
                      paddingLeft: '24px',
                      borderLeft: '1px solid rgba(196, 165, 117, 0.1)',
                      position: 'relative'
                    }}>
                      <span style={{
                        fontSize: '14px',
                        fontFamily: 'Inter',
                        fontWeight: 300,
                        color: '#4A4744'
                      }}>
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div style={{marginTop: '64px'}}>
                <h3 style={{
                  fontSize: '20px',
                  fontFamily: 'Cormorant Garamond',
                  fontWeight: 400,
                  color: '#3D3A37',
                  marginBottom: '28px'
                }}>
                  Formy spotkań
                </h3>
                
                <div>
                  {privateClasses.offerings.map((option, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '20px 0',
                      borderBottom: index === privateClasses.offerings.length - 1 ? 'none' : '1px solid rgba(139, 133, 127, 0.15)'
                    }}>
                      <div>
                        <h4 style={{
                          fontSize: '15px',
                          fontFamily: 'Inter',
                          fontWeight: 400,
                          color: '#3D3A37',
                          margin: '0 0 4px 0'
                        }}>
                          {option.name}
                        </h4>
                        <p style={{
                          fontSize: '13px',
                          fontFamily: 'Inter',
                          fontWeight: 300,
                          color: '#8B857F',
                          margin: 0
                        }}>
                          {option.duration}
                        </p>
                      </div>
                      <span style={{
                        fontSize: '13px',
                        fontFamily: 'Inter',
                        fontWeight: 300,
                        color: '#8B857F'
                      }}>
                        {option.note}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Kolumna wizualna (prawa) */}
            <div style={sectionStyles.imageContainer}>
              <Image
                src="/images/profile/omnie-opt.webp"
                alt="Indywidualne sesje jogi online"
                fill
                style={{
                  objectFit: 'cover',
                  filter: 'grayscale(100%) brightness(1.05)'
                }}
                sizes={isMobile ? '100vw' : '45vw'}
                quality={95}
              />
            </div>
          </div>
        </section>

        {/* LINIA PODZIAŁU */}
        <div style={sectionStyles.divider}></div>

        {/* SEKCJA GRUPOWE - Odwrócona dwukolumnowa */}
        <section style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: isMobile ? '0 40px' : isTablet ? '0 60px' : '0 80px'
        }}>
          <div style={sectionStyles.gridReverse}>
            {/* Kolumna wizualna (lewa) */}
            <div style={sectionStyles.visualElement}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: isMobile ? '10px' : '20px'
              }}>
                <div style={{
                  width: isMobile ? '40px' : '60px',
                  height: isMobile ? '40px' : '60px',
                  border: '1px solid rgba(196, 165, 117, 0.15)',
                  borderRadius: '50%',
                  transform: isMobile ? 'translateX(5px)' : 'translateX(10px)'
                }}></div>
                <div style={{
                  width: isMobile ? '40px' : '60px',
                  height: isMobile ? '40px' : '60px',
                  border: '1px solid rgba(196, 165, 117, 0.15)',
                  borderRadius: '50%',
                  transform: isMobile ? 'translateX(-5px)' : 'translateX(-10px)'
                }}></div>
                <div style={{
                  width: isMobile ? '40px' : '60px',
                  height: isMobile ? '40px' : '60px',
                  border: '1px solid rgba(196, 165, 117, 0.15)',
                  borderRadius: '50%',
                  transform: isMobile ? 'translateX(-15px)' : 'translateX(-30px)'
                }}></div>
              </div>
            </div>

            {/* Kolumna tekstowa (prawa) */}
            <div>
              <h2 style={sectionStyles.sectionTitle}>
                {groupClasses.title}
              </h2>

              <p style={{
                fontSize: '17px',
                fontFamily: 'Inter',
                fontWeight: 300,
                color: '#5B5754',
                lineHeight: '1.8',
                marginBottom: '32px'
              }}>
                {groupClasses.subtitle}
              </p>

              <div style={{
                fontSize: '15px',
                fontFamily: 'Inter',
                fontWeight: 300,
                color: '#3D3A37',
                lineHeight: '1.9',
                letterSpacing: '0.02em',
                marginBottom: '56px',
                textAlign: 'left'
              }}>
                {groupClasses.description}
              </div>

              <div style={{marginBottom: '64px'}}>
                <h3 style={{
                  fontSize: '20px',
                  fontFamily: 'Cormorant Garamond',
                  fontWeight: 400,
                  color: '#3D3A37',
                  marginBottom: '28px'
                }}>
                  Harmonogram zajęć
                </h3>
                
                <div>
                  {groupClasses.schedule.map((session, index) => (
                    <div key={index} style={{
                      padding: '18px 0',
                      fontSize: '14px',
                      fontFamily: 'Inter',
                      color: '#3D3A37'
                    }}>
                      <span style={{fontWeight: 400}}>{session.day}</span>
                      <span style={{color: '#D4D0CC', margin: '0 8px'}}>|</span>
                      <span style={{fontWeight: 300}}>{session.time}</span>
                      <span style={{color: '#D4D0CC', margin: '0 8px'}}>|</span>
                      <span style={{fontWeight: 300, color: '#6B6560'}}>{session.type}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div style={{marginTop: '64px'}}>
                <h3 style={{
                  fontSize: '20px',
                  fontFamily: 'Cormorant Garamond',
                  fontWeight: 400,
                  color: '#3D3A37',
                  marginBottom: '28px'
                }}>
                  Formy spotkań
                </h3>
                
                <div>
                  {groupClasses.offerings.map((option, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '20px 0',
                      borderBottom: index === groupClasses.offerings.length - 1 ? 'none' : '1px solid rgba(139, 133, 127, 0.15)'
                    }}>
                      <div>
                        <h4 style={{
                          fontSize: '15px',
                          fontFamily: 'Inter',
                          fontWeight: 400,
                          color: '#3D3A37',
                          margin: '0 0 4px 0'
                        }}>
                          {option.name}
                        </h4>
                        <p style={{
                          fontSize: '13px',
                          fontFamily: 'Inter',
                          fontWeight: 300,
                          color: '#8B857F',
                          margin: 0
                        }}>
                          {option.size}
                        </p>
                      </div>
                      <span style={{
                        fontSize: '13px',
                        fontFamily: 'Inter',
                        fontWeight: 300,
                        color: '#8B857F'
                      }}>
                        {option.note}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* SEKCJA CTA */}
        <section style={sectionStyles.ctaSection}>
          <h2 style={sectionStyles.ctaTitle}>
            Rozpocznij swoją praktykę
          </h2>

          <p style={{
            fontSize: '16px',
            fontFamily: 'Inter',
            fontWeight: 300,
            color: '#5B5754',
            lineHeight: '1.7',
            marginBottom: '48px'
          }}>
            Skontaktuj się ze mną, aby omówić szczegóły i znaleźć formę praktyki, która będzie odpowiadać Twoim potrzebom.
          </p>

          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '16px'
          }}>
            <button style={{
              padding: '16px 48px',
              border: '1px solid rgba(61, 58, 55, 0.3)',
              backgroundColor: 'transparent',
              fontSize: '12px',
              fontFamily: 'Inter',
              fontWeight: 400,
              letterSpacing: '0.15em',
              textTransform: 'uppercase',
              color: '#3D3A37',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = '#3D3A37';
              e.target.style.color = '#FDFAF7';
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.color = '#3D3A37';
            }}
            onClick={() => window.location.href = '/kontakt'}
            >
              UMÓW SESJĘ
            </button>
            
            <p style={{
              fontSize: '13px',
              fontFamily: 'Inter',
              fontWeight: 300,
              color: '#8B857F',
              margin: 0
            }}>
              Pierwsza konsultacja 30 min bez opłat
            </p>
          </div>
        </section>
      </main>
    </>
  );
}
